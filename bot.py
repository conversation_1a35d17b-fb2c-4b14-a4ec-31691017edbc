import asyncio
import logging
import signal
import sys
import platform
import time
from typing import Optional
from collections import defaultdict, deque
from datetime import datetime, timedelta, timezone
import datetime as dt

import discord
from discord.ext import commands, tasks

from logging_config import setup_logging, important_logger
from utils.config import get_discord_token, get_admin_id, get_guild_id
from services.data.database_service import DatabaseService
from services.market.market_service import get_market_service
from services.core.portfolio_service import get_portfolio_service

logger = logging.getLogger(__name__)

class BotStats:
    def __init__(self):
        self.start_time = datetime.now(timezone.utc)
        self.command_count = defaultdict(int)
        self.error_count = defaultdict(int)
        self.response_times = defaultdict(deque)
        self.api_calls = defaultdict(int)

    def record_command(self, command_name: str, response_time: float):
        self.command_count[command_name] += 1
        self.response_times[command_name].append(response_time)

        if len(self.response_times[command_name]) > 100:
            self.response_times[command_name].popleft()

    def record_error(self, error_type: str):
        self.error_count[error_type] += 1

    def record_api_call(self, api_name: str):
        self.api_calls[api_name] += 1

    def get_uptime(self) -> timedelta:
        return datetime.now(timezone.utc) - self.start_time

    def get_avg_response_time(self, command_name: str) -> float:
        times = self.response_times.get(command_name, [])
        return sum(times) / len(times) if times else 0.0

class CryptoBot(commands.Bot):
    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = True  # Enabled in Developer Portal

        super().__init__(
            command_prefix='!',
            intents=intents,
            help_command=None
        )

        self.db_service = DatabaseService()
        self.market_service = get_market_service()
        self.portfolio_service = get_portfolio_service(self.db_service)

        from handlers.discord.alerts.volatility_alerts import get_volatility_alert_handler
        self.volatility_handler = get_volatility_alert_handler(self)

        from handlers.discord.alerts.market_monitor_alerts import get_market_monitor_alert_handler
        self.market_monitor_handler = get_market_monitor_alert_handler(self)

        from services.market.market_monitor_service import get_market_monitor_service
        self.market_monitor = get_market_monitor_service()

        from services.market.trading_time_service import get_trading_time_service
        self.trading_time_service = get_trading_time_service()

        from handlers.discord.alerts.trading_time_alerts import get_trading_time_alert_handler
        self.trading_time_handler = get_trading_time_alert_handler(self)

        from services.discord.discord_bot_logging import get_discord_bot_logging_service
        self.discord_logger = get_discord_bot_logging_service(self)

        from handlers.discord.alerts.market_news_handler import get_market_news_handler
        self.market_news_handler = get_market_news_handler(self)

        self.admin_id = get_admin_id()
        self.guild_id = get_guild_id()

        self.is_ready = False
        self.stats = BotStats()

        self.active_watchlists = {}

        self.cooldowns = defaultdict(dict)

        self.consecutive_errors = 0
        self.max_consecutive_errors = 5

        self.last_health_check = datetime.now()
        self.health_check_interval = timedelta(minutes=5)

    def check_cooldown(self, user_id: int, command_name: str, cooldown_seconds: int = 5) -> bool:
        now = datetime.now()
        user_cooldowns = self.cooldowns[user_id]

        if command_name in user_cooldowns:
            time_since_last = (now - user_cooldowns[command_name]).total_seconds()
            if time_since_last < cooldown_seconds:
                return False

        user_cooldowns[command_name] = now
        return True

    def get_cooldown_remaining(self, user_id: int, command_name: str, cooldown_seconds: int = 5) -> float:
        now = datetime.now()
        user_cooldowns = self.cooldowns[user_id]

        if command_name in user_cooldowns:
            time_since_last = (now - user_cooldowns[command_name]).total_seconds()
            remaining = cooldown_seconds - time_since_last
            return max(0, remaining)
        return 0

    async def record_command_execution(self, command_name: str, start_time: float, success: bool = True):
        response_time = time.time() - start_time
        self.stats.record_command(command_name, response_time)

        if success:
            self.consecutive_errors = 0
        else:
            self.consecutive_errors += 1
            self.stats.record_error(command_name)

        if response_time > 5.0:
            logger.warning(f"Slow command execution: {command_name} took {response_time:.2f}s")

    async def health_check(self):
        now = datetime.now()
        if (now - self.last_health_check) >= self.health_check_interval:
            self.last_health_check = now

    async def setup_hook(self):
        try:
            await self.load_extension('handlers.discord.market.watchlist_commands')
            await self.load_extension('handlers.discord.market.portfolio_commands')
            await self.load_extension('handlers.discord.market.market_commands')
            await self.load_extension('handlers.discord.admin.admin_commands')
            await self.load_extension('handlers.discord.trading.trading_commands')
            await self.load_extension('handlers.discord.trading.advanced_commands')
        except Exception as e:
            logger.error(f"Error loading extensions: {e}")
            self.stats.record_error("extension_load")

    async def on_ready(self):
        if not self.is_ready:
            self.is_ready = True
            important_logger.info(f"Enhanced Discord bot logged in as {self.user} (ID: {self.user.id})")
            important_logger.info(f"Bot is in {len(self.guilds)} guilds")

            try:
                if self.guild_id and any(guild.id == int(self.guild_id) for guild in self.guilds):
                    guild = discord.Object(id=int(self.guild_id))
                    self.tree.copy_global_to(guild=guild)
                    await self.tree.sync(guild=guild)
                elif len(self.guilds) > 0:
                    guild = self.guilds[0]
                    self.tree.copy_global_to(guild=guild)
                    await self.tree.sync(guild=guild)
                else:
                    logger.warning("Bot not in any guilds, cannot sync commands")
                    important_logger.warning("⚠️ Bot not invited to any Discord servers!")
                    important_logger.info("📋 Use this link to invite bot:")
                    important_logger.info(f"https://discord.com/api/oauth2/authorize?client_id={self.user.id}&permissions=94224&scope=bot%20applications.commands")
            except Exception as e:
                logger.error(f"Error syncing commands: {e}")
                self.stats.record_error("command_sync")

            if not self.watchlist_updater.is_running():
                self.watchlist_updater.start()

            if not self.health_monitor.is_running():
                self.health_monitor.start()

            if not self.daily_market_report.is_running():
                self.daily_market_report.start()

            if not self.trading_time_monitor.is_running():
                self.trading_time_monitor.start()

            if not self.optimal_time_analysis.is_running():
                self.optimal_time_analysis.start()

            if not self.market_session_monitor.is_running():
                self.market_session_monitor.start()

            from services.market.market_service import register_volatility_alert_callback
            register_volatility_alert_callback(self.volatility_handler.send_volatility_alert)

            self.market_monitor.register_alert_callback(self.market_monitor_handler.send_market_alert)

            self.trading_time_service.register_alert_callback(self.trading_time_handler.send_trading_time_alert)

            await self.market_monitor.start_monitoring()

            # Start economic calendar service
            try:
                from services.market.economic_calendar_service import get_economic_calendar_service
                self.economic_calendar_service = get_economic_calendar_service(self)
                await self.economic_calendar_service.start_scheduled_tasks()
                important_logger.info("✅ Economic calendar service started successfully")
            except Exception as e:
                logger.error(f"Error starting economic calendar service: {e}")
                important_logger.error(f"❌ Failed to start economic calendar service: {e}")

            important_logger.info("🚀 Enhanced Discord bot is ready and operational!")
            important_logger.info(f"📊 Available commands: /watchlist, /portfolio, /holdings, /add_trade, /market, /rates, /calendar, /c, /p, /stats, /pin, /unpin, /pins")
            important_logger.info(f"💰 Trading commands: /l, /s, /ql, /qs, /scl, /scs, /swl, /sws, /cancel, /orders, /balance (only in #trade channel)")
            important_logger.info(f"📊 Advanced commands: /positions, /pnl, /close, /history (Phase 3 features)")
            important_logger.info(f"🕐 Trading time analysis: Monitoring {self.trading_time_service.top_coins_limit} coins for optimal trading opportunities")

            try:
                await self.discord_logger.send_status_notification(
                    "ready",
                    "🚀 **ChartFix Bot đã sẵn sàng hoạt động!**",
                    f"📊 Kết nối thành công đến {len(self.guilds)} server\n"
                    f"⚡ Các lệnh có sẵn: /watchlist, /portfolio, /market, /rates, /calendar, /c, /p"
                )
            except Exception as e:
                logger.error(f"Failed to send ready notification: {e}")

    async def on_command_error(self, ctx, error):
        if isinstance(error, commands.CommandNotFound):
            return

        if isinstance(error, commands.CommandOnCooldown):
            await ctx.send(f"⏰ Command on cooldown. Try again in {error.retry_after:.1f} seconds.", ephemeral=True)
            return

        if isinstance(error, commands.MissingPermissions):
            await ctx.send("❌ You don't have permission to use this command.", ephemeral=True)
            return

        logger.error(f"Command error in {ctx.command}: {type(error).__name__}: {error}")
        self.stats.record_error(f"command_{type(error).__name__}")

        error_msg = "❌ An error occurred while processing your command."

        if "timeout" in str(error).lower():
            error_msg = "⏱️ Command timed out. Please try again."
        elif "forbidden" in str(error).lower():
            error_msg = "🚫 Bot doesn't have permission to perform this action."
        elif "not found" in str(error).lower():
            error_msg = "🔍 Requested resource not found."

        if ctx.interaction:
            if not ctx.interaction.response.is_done():
                await ctx.interaction.response.send_message(error_msg, ephemeral=True)
            else:
                await ctx.interaction.followup.send(error_msg, ephemeral=True)
        else:
            await ctx.send(error_msg)

    async def on_application_command_error(self, interaction: discord.Interaction, error):
        logger.error(f"Slash command error: {type(error).__name__}: {error}")
        self.stats.record_error(f"slash_{type(error).__name__}")

        error_msg = "❌ An error occurred while processing your command."

        if "timeout" in str(error).lower():
            error_msg = "⏱️ Command timed out. Please try again."
        elif "forbidden" in str(error).lower():
            error_msg = "🚫 Bot doesn't have permission to perform this action."
        elif "rate limit" in str(error).lower():
            error_msg = "🐌 Rate limited. Please wait a moment and try again."

        try:
            if not interaction.response.is_done():
                await interaction.response.send_message(error_msg, ephemeral=True)
            else:
                await interaction.followup.send(error_msg, ephemeral=True)
        except Exception as e:
            logger.error(f"Failed to send error message: {e}")

    async def on_error(self, event, *_args, **_kwargs):
        logger.exception(f"Bot error in event {event}")
        self.stats.record_error(f"event_{event}")

        try:
            await self.discord_logger.send_error_notification(
                f"Event Error: {event}",
                f"Lỗi xảy ra trong event {event}",
                event,
                "ERROR"
            )
        except Exception as e:
            logger.error(f"Failed to send error notification: {e}")

        if self.consecutive_errors >= self.max_consecutive_errors:
            logger.critical(f"Too many consecutive errors ({self.consecutive_errors}), attempting recovery")
            important_logger.warning("🚨 Bot entering recovery mode due to excessive errors")

            try:
                await self.discord_logger.send_error_notification(
                    "Critical System Error",
                    f"Bot gặp quá nhiều lỗi liên tiếp ({self.consecutive_errors}). Đang khởi động lại hệ thống...",
                    "error_recovery",
                    "CRITICAL"
                )
            except Exception as e:
                logger.error(f"Failed to send critical error notification: {e}")

            self.consecutive_errors = 0
            await asyncio.sleep(5)

    def is_admin(self, user_id: int) -> bool:
        return str(user_id) == self.admin_id

    @tasks.loop(seconds=90)
    async def watchlist_updater(self):
        try:
            await self.health_check()

            if self.active_watchlists:
                watchlist_data = await self.market_service.get_watchlist_data()

                if watchlist_data['success']:
                    for channel_id, message_id in list(self.active_watchlists.items()):
                        try:
                            channel = self.get_channel(channel_id)
                            if channel:
                                message = await channel.fetch_message(message_id)

                                from utils.ui_components import format_watchlist_message
                                embed_content = format_watchlist_message(watchlist_data)

                                embed = discord.Embed(
                                    title="🚀 CRYPTO WATCHLIST 📈",
                                    description=embed_content,
                                    color=0x00ff88,
                                    timestamp=discord.utils.utcnow()
                                )
                                embed.set_footer(text="Dữ liệu từ Binance Futures • Tự động cập nhật mỗi 90s")

                                await message.edit(embed=embed)
                            else:
                                del self.active_watchlists[channel_id]

                        except discord.NotFound:
                            del self.active_watchlists[channel_id]
                        except Exception as e:
                            logger.error(f"Error updating watchlist in channel {channel_id}: {e}")

                self.stats.record_api_call("watchlist_update")

        except Exception as e:
            logger.error(f"Error in watchlist updater: {e}")
            self.stats.record_error("watchlist_updater")

    @tasks.loop(minutes=5)
    async def health_monitor(self):
        try:
            await self.health_check()

            now = datetime.now()
            cutoff = now - timedelta(hours=1)

            for user_id in list(self.cooldowns.keys()):
                user_cooldowns = self.cooldowns[user_id]
                for command in list(user_cooldowns.keys()):
                    if user_cooldowns[command] < cutoff:
                        del user_cooldowns[command]

                if not user_cooldowns:
                    del self.cooldowns[user_id]

        except Exception as e:
            logger.error(f"Error in health monitor: {e}")
            self.stats.record_error("health_monitor")

    @watchlist_updater.before_loop
    async def before_watchlist_updater(self):
        await self.wait_until_ready()

    @health_monitor.before_loop
    async def before_health_monitor(self):
        await self.wait_until_ready()

    @tasks.loop(time=dt.time(hour=0, minute=0))
    async def daily_market_report(self):
        try:
            success = await self.market_news_handler.send_daily_market_report()

            if success:
                self.stats.record_api_call("daily_market_report")
            else:
                logger.error("❌ Failed to send daily market report")
                self.stats.record_error("daily_market_report")

        except Exception as e:
            logger.error(f"Error in daily market report task: {e}")
            self.stats.record_error("daily_market_report")

    @daily_market_report.before_loop
    async def before_daily_market_report(self):
        await self.wait_until_ready()

    @tasks.loop(minutes=5)
    async def trading_time_monitor(self):
        try:
            volume_surges = await self.trading_time_service.detect_volume_surges()
            if volume_surges:
                logger.info(f"Detected {len(volume_surges)} volume surges")

            price_movements = await self.trading_time_service.detect_price_movements()
            if price_movements:
                logger.info(f"Detected {len(price_movements)} price movements")

            news_opportunities = await self.trading_time_service.get_news_trading_opportunities()
            if news_opportunities:
                logger.info(f"Found {len(news_opportunities)} news trading opportunities")

        except Exception as e:
            logger.error(f"Error in trading time monitor: {e}")
            self.stats.record_error("trading_time_monitor")

    @trading_time_monitor.before_loop
    async def before_trading_time_monitor(self):
        await self.wait_until_ready()

    @tasks.loop(hours=4)
    async def optimal_time_analysis(self):
        try:
            session_info = await self.trading_time_service.get_current_market_session()
            volume_analysis = await self.trading_time_service.analyze_volume_patterns()

            analysis_data = {
                'session_info': session_info,
                'volume_analysis': volume_analysis
            }

            await self.trading_time_handler.send_trading_time_alert('optimal_time_analysis', analysis_data)
            logger.info("Sent optimal time analysis update")
            self.stats.record_api_call("optimal_time_analysis")

        except Exception as e:
            logger.error(f"Error in optimal time analysis: {e}")
            self.stats.record_error("optimal_time_analysis")

    @optimal_time_analysis.before_loop
    async def before_optimal_time_analysis(self):
        await self.wait_until_ready()

    @tasks.loop(hours=1)  # Check market session changes every hour
    async def market_session_monitor(self):
        try:
            session_info = await self.trading_time_service.get_current_market_session()

            # Send market session status update
            session_data = {'session_info': session_info}
            await self.trading_time_handler.send_trading_time_alert('market_session', session_data)

            logger.info("Updated market session status")
            self.stats.record_api_call("market_session_update")

        except Exception as e:
            logger.error(f"Error in market session monitor: {e}")
            self.stats.record_error("market_session_monitor")

    @market_session_monitor.before_loop
    async def before_market_session_monitor(self):
        await self.wait_until_ready()

bot: Optional[CryptoBot] = None

async def main():
    global bot

    try:
        token = get_discord_token()

        if not token:
            logger.error("DISCORD_BOT_TOKEN not found in config.yaml")
            important_logger.error("Error: DISCORD_BOT_TOKEN not configured in config.yaml")
            sys.exit(1)

        setup_logging()
        important_logger.info("Starting Discord bot...")

        bot = CryptoBot()

        def signal_handler(sig_name):
            important_logger.info(f"Received {sig_name}, shutting down...")
            asyncio.create_task(shutdown())

        if platform.system() != 'Windows':
            loop = asyncio.get_event_loop()
            for sig in [signal.SIGINT, signal.SIGTERM, signal.SIGHUP]:
                loop.add_signal_handler(sig, lambda s=sig: signal_handler(s.name))

        await bot.start(token)

    except KeyboardInterrupt:
        important_logger.info("Bot interrupted by user")
    except Exception as e:
        logger.exception(f"Error starting bot: {e}")
        important_logger.error(f"Error starting bot: {e}")
        sys.exit(1)

async def shutdown():
    global bot

    if not bot:
        return

    important_logger.info("Shutting down Discord bot...")

    try:
        await bot.discord_logger.send_status_notification(
            "shutdown",
            "🛑 **ChartFix Bot đang tắt...**",
            "Hệ thống sẽ ngừng hoạt động trong giây lát"
        )
    except Exception as e:
        logger.error(f"Failed to send shutdown notification: {e}")

    # Cancel all running tasks
    bot.watchlist_updater.cancel()
    bot.daily_market_report.cancel()
    bot.trading_time_monitor.cancel()
    bot.optimal_time_analysis.cancel()
    bot.market_session_monitor.cancel()

    try:
        await bot.economic_calendar_service.stop_scheduled_tasks()
        important_logger.info("✅ Economic calendar service stopped")
    except Exception as e:
        logger.error(f"Error stopping economic calendar service: {e}")

    try:
        from services.core.http_client_service import close_http_client
        await close_http_client()
        important_logger.info("✅ HTTP client service closed")
    except Exception as e:
        logger.error(f"Error closing HTTP client service: {e}")

    try:
        await bot.close()
        # Give extra time for Discord connections to close properly
        await asyncio.sleep(0.5)
        important_logger.info("Discord bot shutdown complete")
    except Exception as e:
        logger.error(f"Error during bot shutdown: {e}")
        important_logger.info("Discord bot shutdown completed with errors")

if __name__ == "__main__":
    try:
        if platform.system() == 'Windows':
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

        asyncio.run(main())

    except KeyboardInterrupt:
        print("Bot stopped by user")
        # Ensure proper cleanup on Ctrl+C
        try:
            asyncio.run(shutdown())
        except Exception as cleanup_error:
            logger.error(f"Error during cleanup: {cleanup_error}")
    except Exception as e:
        logger.exception("Unhandled exception:")
        # Ensure cleanup even on unexpected errors
        try:
            asyncio.run(shutdown())
        except Exception as cleanup_error:
            logger.error(f"Error during cleanup: {cleanup_error}")
        sys.exit(1)
