import sqlite3
import logging
import threading
from typing import Optional, List, Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class DatabaseService:
    def __init__(self, db_path: str = "chartfix.db"):
        self.db_path = db_path
        self.lock = threading.RLock()
        self._init_database()

    def _init_database(self):
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS portfolio (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id TEXT NOT NULL,
                        symbol TEXT NOT NULL,
                        quantity REAL NOT NULL,
                        avg_price REAL NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS trades (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id TEXT NOT NULL,
                        symbol TEXT NOT NULL,
                        side TEXT NOT NULL,
                        quantity REAL NOT NULL,
                        price REAL NOT NULL,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS volatility_alerts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp INTEGER,
                        symbol TEXT,
                        timeframe TEXT,
                        price_change REAL,
                        threshold REAL,
                        current_price REAL,
                        previous_price REAL,
                        message TEXT,
                        sent BOOLEAN DEFAULT 0
                    )
                ''')

                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS alert_cooldowns (
                        symbol TEXT PRIMARY KEY,
                        last_alert_time INTEGER
                    )
                ''')

                conn.commit()
                logger.info("Database initialized successfully")

        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            raise

    def execute_query(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        with self.lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    conn.row_factory = sqlite3.Row
                    cursor = conn.cursor()
                    cursor.execute(query, params)
                    return [dict(row) for row in cursor.fetchall()]
            except Exception as e:
                logger.error(f"Error executing query: {e}")
                raise

    def execute_update(self, query: str, params: tuple = ()) -> int:
        with self.lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute(query, params)
                    conn.commit()
                    return cursor.rowcount
            except Exception as e:
                logger.error(f"Error executing update: {e}")
                raise

    def get_portfolio(self, user_id: str) -> List[Dict[str, Any]]:
        query = "SELECT * FROM portfolio WHERE user_id = ?"
        return self.execute_query(query, (user_id,))

    def add_trade(self, user_id: str, symbol: str, side: str, quantity: float, price: float) -> bool:
        try:
            query = "INSERT INTO trades (user_id, symbol, side, quantity, price) VALUES (?, ?, ?, ?, ?)"
            self.execute_update(query, (user_id, symbol, side, quantity, price))
            return True
        except Exception as e:
            logger.error(f"Error adding trade: {e}")
            return False

    def update_portfolio(self, user_id: str, symbol: str, quantity: float, avg_price: float) -> bool:
        try:
            existing = self.execute_query(
                "SELECT * FROM portfolio WHERE user_id = ? AND symbol = ?",
                (user_id, symbol)
            )

            if existing:
                query = "UPDATE portfolio SET quantity = ?, avg_price = ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ? AND symbol = ?"
                self.execute_update(query, (quantity, avg_price, user_id, symbol))
            else:
                query = "INSERT INTO portfolio (user_id, symbol, quantity, avg_price) VALUES (?, ?, ?, ?)"
                self.execute_update(query, (user_id, symbol, quantity, avg_price))

            return True
        except Exception as e:
            logger.error(f"Error updating portfolio: {e}")
            return False
