#!/usr/bin/env python3
"""
Database Service for Order & Position Tracking
SQLite database with tables: orders, positions, users, notifications
"""

import sqlite3
import json
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from contextlib import contextmanager

logger = logging.getLogger(__name__)

class TradingDatabase:
    def __init__(self, db_path: str = "trading.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """Initialize database with required tables"""
        try:
            with self.get_connection() as conn:
                # Users table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS users (
                        user_id TEXT PRIMARY KEY,
                        username TEXT,
                        created_at TEXT,
                        settings TEXT,
                        total_pnl REAL DEFAULT 0.0,
                        total_trades INTEGER DEFAULT 0
                    )
                """)

                # Orders table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS orders (
                        order_id TEXT PRIMARY KEY,
                        user_id TEXT,
                        symbol TEXT,
                        side TEXT,
                        type TEXT,
                        amount REAL,
                        price REAL,
                        status TEXT,
                        position_side TEXT,
                        created_at TEXT,
                        filled_at TEXT,
                        filled_price REAL,
                        filled_amount REAL DEFAULT 0.0,
                        tp_order_id TEXT,
                        sl_order_id TEXT,
                        strategy TEXT,
                        metadata TEXT,
                        FOREIGN KEY (user_id) REFERENCES users (user_id)
                    )
                """)

                # Positions table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS positions (
                        position_id TEXT PRIMARY KEY,
                        user_id TEXT,
                        symbol TEXT,
                        side TEXT,
                        position_side TEXT,
                        amount REAL,
                        entry_price REAL,
                        current_price REAL,
                        unrealized_pnl REAL DEFAULT 0.0,
                        realized_pnl REAL DEFAULT 0.0,
                        margin_used REAL DEFAULT 0.0,
                        created_at TEXT,
                        updated_at TEXT,
                        orders TEXT,
                        status TEXT DEFAULT 'open',
                        FOREIGN KEY (user_id) REFERENCES users (user_id)
                    )
                """)

                # Notifications table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS notifications (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id TEXT,
                        type TEXT,
                        title TEXT,
                        message TEXT,
                        data TEXT,
                        sent_at TEXT,
                        channel_id TEXT,
                        FOREIGN KEY (user_id) REFERENCES users (user_id)
                    )
                """)

                # Create indexes for better performance
                conn.execute("CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders (user_id)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_orders_status ON orders (status)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_positions_user_id ON positions (user_id)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_positions_status ON positions (status)")

                conn.commit()
                logger.info("✅ Database initialized successfully")

        except Exception as e:
            logger.error(f"❌ Database initialization failed: {e}")
            raise

    @contextmanager
    def get_connection(self):
        """Context manager for database connections"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Enable dict-like access
        try:
            yield conn
        finally:
            conn.close()

    def create_user(self, user_id: str, username: str, settings: Dict = None) -> bool:
        """Create or update user record"""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO users
                    (user_id, username, created_at, settings)
                    VALUES (?, ?, ?, ?)
                """, (
                    user_id,
                    username,
                    datetime.now(timezone.utc).isoformat(),
                    json.dumps(settings or {})
                ))
                conn.commit()
                logger.info(f"✅ User created/updated: {username}")
                return True
        except Exception as e:
            logger.error(f"❌ Failed to create user: {e}")
            return False

    def get_user(self, user_id: str) -> Optional[Dict]:
        """Get user by ID"""
        try:
            with self.get_connection() as conn:
                row = conn.execute(
                    "SELECT * FROM users WHERE user_id = ?", (user_id,)
                ).fetchone()

                if row:
                    user = dict(row)
                    user['settings'] = json.loads(user['settings'] or '{}')
                    return user
                return None
        except Exception as e:
            logger.error(f"❌ Failed to get user: {e}")
            return None

    def get_users(self) -> List[Dict]:
        """Get all users"""
        try:
            with self.get_connection() as conn:
                rows = conn.execute("SELECT * FROM users ORDER BY created_at DESC").fetchall()
                users = []
                for row in rows:
                    user = dict(row)
                    user['settings'] = json.loads(user['settings'] or '{}')
                    users.append(user)
                return users
        except Exception as e:
            logger.error(f"❌ Failed to get users: {e}")
            return []

    def create_order(self, order_data: Dict) -> bool:
        """Create order record"""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    INSERT INTO orders (
                        order_id, user_id, symbol, side, type, amount, price,
                        status, position_side, created_at, tp_order_id, sl_order_id,
                        strategy, metadata
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    order_data['order_id'],
                    order_data['user_id'],
                    order_data['symbol'],
                    order_data['side'],
                    order_data['type'],
                    order_data['amount'],
                    order_data['price'],
                    order_data['status'],
                    order_data['position_side'],
                    order_data['created_at'],
                    order_data.get('tp_order_id'),
                    order_data.get('sl_order_id'),
                    order_data.get('strategy', 'manual'),
                    json.dumps(order_data.get('metadata', {}))
                ))
                conn.commit()
                logger.info(f"✅ Order created: {order_data['order_id']}")
                return True
        except Exception as e:
            logger.error(f"❌ Failed to create order: {e}")
            return False

    def update_order_status(self, order_id: str, status: str,
                           filled_data: Dict = None) -> bool:
        """Update order status and fill data"""
        try:
            with self.get_connection() as conn:
                if filled_data:
                    conn.execute("""
                        UPDATE orders SET
                        status = ?, filled_at = ?, filled_price = ?, filled_amount = ?
                        WHERE order_id = ?
                    """, (
                        status,
                        filled_data.get('filled_at'),
                        filled_data.get('filled_price'),
                        filled_data.get('filled_amount'),
                        order_id
                    ))
                else:
                    conn.execute("""
                        UPDATE orders SET status = ? WHERE order_id = ?
                    """, (status, order_id))

                conn.commit()
                logger.info(f"✅ Order status updated: {order_id} -> {status}")
                return True
        except Exception as e:
            logger.error(f"❌ Failed to update order status: {e}")
            return False

    def get_orders(self, user_id: str = None, status: str = None) -> List[Dict]:
        """Get orders with optional filters"""
        try:
            with self.get_connection() as conn:
                query = "SELECT * FROM orders WHERE 1=1"
                params = []

                if user_id:
                    query += " AND user_id = ?"
                    params.append(user_id)

                if status:
                    query += " AND status = ?"
                    params.append(status)

                query += " ORDER BY created_at DESC"

                rows = conn.execute(query, params).fetchall()
                orders = []
                for row in rows:
                    order = dict(row)
                    order['metadata'] = json.loads(order['metadata'] or '{}')
                    orders.append(order)

                return orders
        except Exception as e:
            logger.error(f"❌ Failed to get orders: {e}")
            return []

    def log_notification(self, user_id: str, notification_type: str,
                        title: str, message: str, data: Dict = None,
                        channel_id: str = None) -> bool:
        """Log notification to database"""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    INSERT INTO notifications
                    (user_id, type, title, message, data, sent_at, channel_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    user_id,
                    notification_type,
                    title,
                    message,
                    json.dumps(data or {}),
                    datetime.now(timezone.utc).isoformat(),
                    channel_id
                ))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"❌ Failed to log notification: {e}")
            return False

# Global database instance
trading_db = TradingDatabase()
