import logging
import threading
from typing import Dict, List, Any
from services.data.database_service import DatabaseService
from services.market.market_service import get_market_service
from services.core.error_service import handle_service_errors

logger = logging.getLogger(__name__)

class PortfolioService:
    def __init__(self, db_service: DatabaseService):
        self.db_service = db_service
        self.market_service = get_market_service()
        self.logger = logger

    @handle_service_errors
    async def get_portfolio(self, user_id: str) -> List[Dict[str, Any]]:
        try:
            portfolio = self.db_service.get_portfolio(user_id)

            if not portfolio:
                return []

            symbols = [position['symbol'] for position in portfolio]
            current_prices = await self.market_service.get_prices(symbols)
            if not current_prices:
                current_prices = {symbol: 0 for symbol in symbols}

            for position in portfolio:
                symbol = position['symbol']
                current_price = current_prices.get(symbol, 0)

                position['current_price'] = current_price
                position['current_value'] = position['quantity'] * current_price
                position['cost_basis'] = position['quantity'] * position['avg_price']
                position['pnl'] = position['current_value'] - position['cost_basis']
                position['pnl_percentage'] = (
                    (position['pnl'] / position['cost_basis']) * 100
                    if position['cost_basis'] > 0 else 0
                )

            return portfolio

        except Exception as e:
            self.logger.error(f"Error getting portfolio: {e}")
            return []

    @handle_service_errors
    def add_position(self, user_id: str, symbol: str, quantity: float, price: float) -> bool:
        try:
            existing = self.db_service.execute_query(
                "SELECT * FROM portfolio WHERE user_id = ? AND symbol = ?",
                (user_id, symbol)
            )

            if existing:
                old_position = existing[0]
                old_quantity = old_position['quantity']
                old_avg_price = old_position['avg_price']

                total_cost = (old_quantity * old_avg_price) + (quantity * price)
                new_quantity = old_quantity + quantity
                new_avg_price = total_cost / new_quantity if new_quantity > 0 else 0

                return self.db_service.update_portfolio(user_id, symbol, new_quantity, new_avg_price)
            else:
                return self.db_service.update_portfolio(user_id, symbol, quantity, price)

        except Exception as e:
            self.logger.error(f"Error adding position: {e}")
            return False

    def remove_position(self, user_id: str, symbol: str, quantity: float) -> bool:
        try:
            existing = self.db_service.execute_query(
                "SELECT * FROM portfolio WHERE user_id = ? AND symbol = ?",
                (user_id, symbol)
            )

            if not existing:
                return False

            old_position = existing[0]
            old_quantity = old_position['quantity']

            if quantity >= old_quantity:
                self.db_service.execute_update(
                    "DELETE FROM portfolio WHERE user_id = ? AND symbol = ?",
                    (user_id, symbol)
                )
            else:
                new_quantity = old_quantity - quantity
                self.db_service.execute_update(
                    "UPDATE portfolio SET quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ? AND symbol = ?",
                    (new_quantity, user_id, symbol)
                )

            return True

        except Exception as e:
            self.logger.error(f"Error removing position: {e}")
            return False

    @handle_service_errors
    async def get_portfolio_summary(self, user_id: str) -> Dict[str, Any]:
        try:
            portfolio = await self.get_portfolio(user_id)

            if not portfolio:
                return {
                    'success': True,
                    'data': {
                        'total_value': 0,
                        'total_cost': 0,
                        'total_pnl': 0,
                        'total_pnl_percentage': 0,
                        'positions_count': 0,
                        'positions': []
                    },
                    'message': 'Portfolio is empty'
                }

            total_value = sum(pos['current_value'] for pos in portfolio)
            total_cost = sum(pos['cost_basis'] for pos in portfolio)
            total_pnl = total_value - total_cost
            total_pnl_percentage = (total_pnl / total_cost) * 100 if total_cost > 0 else 0

            return {
                'success': True,
                'data': {
                    'total_value': total_value,
                    'total_cost': total_cost,
                    'total_pnl': total_pnl,
                    'total_pnl_percentage': total_pnl_percentage,
                    'positions_count': len(portfolio),
                    'positions': portfolio
                },
                'message': 'Portfolio summary retrieved successfully'
            }

        except Exception as e:
            self.logger.error(f"Error getting portfolio summary: {e}")
            return {
                'success': False,
                'data': {
                    'total_value': 0,
                    'total_cost': 0,
                    'total_pnl': 0,
                    'total_pnl_percentage': 0,
                    'positions_count': 0,
                    'positions': []
                },
                'message': f'Error retrieving portfolio: {str(e)}'
            }

    async def get_holdings(self, user_id: str) -> Dict[str, Any]:
        try:
            portfolio = await self.get_portfolio(user_id)

            return {
                'success': True,
                'data': portfolio,
                'message': 'Holdings retrieved successfully'
            }

        except Exception as e:
            self.logger.error(f"Error getting holdings: {e}")
            return {
                'success': False,
                'data': [],
                'message': f'Error retrieving holdings: {str(e)}'
            }

    def add_trade(self, user_id: str, symbol: str, quantity: float, price: float) -> Dict[str, Any]:
        try:
            if not symbol or quantity <= 0 or price <= 0:
                return {
                    'success': False,
                    'message': 'Invalid trade parameters'
                }

            from services.core.symbol_service import normalize_symbol
            symbol = normalize_symbol(symbol.upper())

            success = self.add_position(user_id, symbol, quantity, price)

            if success:
                return {
                    'success': True,
                    'message': 'Trade added successfully'
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to add trade to database'
                }

        except Exception as e:
            self.logger.error(f"Error adding trade: {e}")
            return {
                'success': False,
                'message': f'Error adding trade: {str(e)}'
            }

_portfolio_service = None
_instance_lock = threading.RLock()

def get_portfolio_service(db_service: DatabaseService) -> PortfolioService:
    global _portfolio_service
    with _instance_lock:
        if _portfolio_service is None:
            _portfolio_service = PortfolioService(db_service)
        return _portfolio_service
