import logging
import re
import threading
from typing import List, Tuple, Dict, Optional

logger = logging.getLogger(__name__)

class SymbolService:
    """
    Dịch vụ xử lý symbol thống nhất cho toàn bộ hệ thống.
    Cung cấp các phương thức để chuẩn hóa, <PERSON><PERSON><PERSON> thực và định dạng symbol.
    """

    def __init__(self):
        self.valid_symbols_cache = set()
        self.cache_lock = threading.RLock()

    def normalize_symbol(self, symbol: str) -> str:
        """
        Chuẩn hóa symbol thành dạng BTCUSDT.

        Args:
            symbol: Symbol cần chuẩn hóa

        Returns:
            Symbol đã chuẩn hóa
        """
        if not symbol or not isinstance(symbol, str):
            return ""

        symbol = symbol.upper().strip()

        # Xử lý các trường hợp đặc biệt
        symbol = symbol.replace('/', '')

        # Thêm USDT nếu cần
        if not symbol.endswith('USDT'):
            symbol = f"{symbol}USDT"

        return symbol

    def smart_normalize_symbol(self, symbol: str) -> str:
        """
        Smart symbol normalization for chart and price commands.
        - If symbol is a base token (e.g., "btc", "eth"), append "USDT"
        - If symbol is already a trading pair (e.g., "btcusdc", "ethbtc"), use as-is

        Args:
            symbol: Symbol to normalize

        Returns:
            Normalized symbol
        """
        if not symbol or not isinstance(symbol, str):
            return ""

        symbol = symbol.upper().strip().replace('/', '')

        # Common quote currencies for trading pairs
        quote_currencies = {
            'USDT', 'USDC', 'BUSD', 'BTC', 'ETH', 'BNB', 'FDUSD',
            'TUSD', 'DAI', 'USDD', 'EUR', 'GBP', 'AUD', 'TRY'
        }

        # Check if symbol already contains a quote currency
        for quote in quote_currencies:
            if symbol.endswith(quote) and len(symbol) > len(quote):
                # Symbol already has a quote currency, return as-is
                return symbol

        # Check if symbol looks like a trading pair (base + quote)
        # Common patterns: BTCETH, ETHBTC, ADABNB, etc.
        if len(symbol) >= 6:  # Minimum length for a trading pair
            # Check for common base currencies at the end
            for quote in ['BTC', 'ETH', 'BNB']:
                if symbol.endswith(quote) and len(symbol) > len(quote):
                    # Looks like a trading pair with crypto quote
                    return symbol

        # If we get here, it's likely a base token, append USDT
        return f"{symbol}USDT"

    def format_symbol_for_exchange(self, symbol: str) -> str:
        """
        Chuyển đổi symbol từ BTCUSDT sang BTC/USDT cho Futures API.

        Args:
            symbol: Symbol cần chuyển đổi

        Returns:
            Symbol đã chuyển đổi cho Futures
        """
        if not symbol or not isinstance(symbol, str):
            logger.error(f"Invalid symbol input for formatting: {symbol}")
            return ""

        symbol = self.normalize_symbol(symbol)
        if not symbol:
            logger.error("Symbol normalization returned empty string")
            return ""

        formatted = symbol.replace("USDT", "/USDT")
        if formatted == "/USDT":
            logger.error(f"Invalid formatted symbol result: '{formatted}' from input '{symbol}'")
            return ""

        return formatted

    def is_valid_symbol(self, symbol: str) -> bool:
        """
        Kiểm tra tính hợp lệ của symbol.

        Args:
            symbol: Symbol cần kiểm tra

        Returns:
            True nếu symbol hợp lệ, False nếu không
        """
        if not symbol or not isinstance(symbol, str):
            return False

        # Chuẩn hóa symbol
        symbol = self.normalize_symbol(symbol)

        # Kiểm tra định dạng
        pattern = r'^[A-Z0-9]{2,10}USDT$'
        return bool(re.match(pattern, symbol))

    def get_valid_symbols(self, symbols: List[str]) -> Tuple[List[str], List[str], List[str]]:
        """
        Lọc và chuẩn hóa danh sách symbols.

        Args:
            symbols: Danh sách symbols cần lọc

        Returns:
            Tuple gồm (valid_symbols, invalid_symbols, formatted_valid_symbols)
        """
        valid_symbols = []
        invalid_symbols = []
        formatted_valid_symbols = []

        for symbol in symbols:
            if not symbol or not isinstance(symbol, str):
                continue

            # Chuẩn hóa symbol
            normalized = self.normalize_symbol(symbol)

            # Kiểm tra tính hợp lệ
            if self.is_valid_symbol(normalized):
                valid_symbols.append(normalized)
                formatted_valid_symbols.append(self.format_symbol_for_exchange(normalized))
            else:
                invalid_symbols.append(normalized)

        return valid_symbols, invalid_symbols, formatted_valid_symbols
    def get_symbol_for_exchange(self, symbol: str, exchange: str = 'binance') -> str:
        """
        Get properly formatted symbol for specific exchange.

        Args:
            symbol: Symbol to format
            exchange: Target exchange ('binance', 'coingecko', etc.)

        Returns:
            Formatted symbol for the specified exchange
        """
        if not symbol or not isinstance(symbol, str):
            return ""

        normalized = self.smart_normalize_symbol(symbol)

        if exchange.lower() == 'binance':
            return self.format_symbol_for_exchange(normalized)
        elif exchange.lower() == 'coingecko':
            from utils.symbol_mappings import get_coingecko_id
            return get_coingecko_id(normalized)
        else:
            return normalized

    def validate_trading_pair(self, symbol: str) -> bool:
        """
        Validate if symbol is a valid trading pair.

        Args:
            symbol: Symbol to validate

        Returns:
            True if valid trading pair, False otherwise
        """
        if not symbol or not isinstance(symbol, str):
            return False

        normalized = self.smart_normalize_symbol(symbol)

        # Check basic format
        if not self.is_valid_symbol(normalized):
            return False

        # Extract base and quote
        base, quote = self.get_base_quote_pair(normalized)

        # Validate base and quote currencies
        return len(base) >= 2 and len(quote) >= 3

    def get_base_quote_pair(self, symbol: str) -> Tuple[str, str]:
        """
        Extract base and quote currencies from symbol.

        Args:
            symbol: Trading pair symbol

        Returns:
            Tuple of (base_currency, quote_currency)
        """
        if not symbol or not isinstance(symbol, str):
            return "", ""

        normalized = self.smart_normalize_symbol(symbol)

        # Common quote currencies (ordered by priority)
        quote_currencies = ['USDT', 'USDC', 'BUSD', 'FDUSD', 'TUSD', 'DAI', 'BTC', 'ETH', 'BNB']

        for quote in quote_currencies:
            if normalized.endswith(quote) and len(normalized) > len(quote):
                base = normalized[:-len(quote)]
                return base, quote

        # If no quote currency found, assume USDT
        if normalized.endswith('USDT'):
            return normalized[:-4], 'USDT'
        else:
            return normalized, 'USDT'

    def get_unified_symbol_mapping(self, symbol: str, target_api: str) -> str:
        """
        Unified symbol mapping for different APIs.

        Args:
            symbol: Symbol to map
            target_api: Target API ('coingecko', 'binance', 'yahoo', etc.)

        Returns:
            Mapped symbol for the target API
        """
        if not symbol or not isinstance(symbol, str):
            return ""

        normalized = self.smart_normalize_symbol(symbol)
        base, quote = self.get_base_quote_pair(normalized)

        if target_api.lower() == 'coingecko':
            from utils.symbol_mappings import get_coingecko_id
            return get_coingecko_id(base.lower())
        elif target_api.lower() == 'binance':
            return self.format_symbol_for_exchange(normalized)
        elif target_api.lower() == 'yahoo':
            # Yahoo Finance format: BTC-USD
            if quote in ['USDT', 'USDC', 'BUSD']:
                return f"{base}-USD"
            else:
                return f"{base}-{quote}"
        else:
            return normalized

    def validate_symbol_across_apis(self, symbol: str) -> Dict[str, bool]:
        """
        Check symbol validity across all integrated APIs.

        Args:
            symbol: Symbol to validate

        Returns:
            Dictionary with API names as keys and validity as values
        """
        if not symbol or not isinstance(symbol, str):
            return {}

        normalized = self.smart_normalize_symbol(symbol)
        base, quote = self.get_base_quote_pair(normalized)

        results = {}

        # Binance validation
        results['binance'] = self.validate_trading_pair(normalized)

        # CoinGecko validation
        try:
            from utils.symbol_mappings import is_symbol_supported
            results['coingecko'] = is_symbol_supported(base.lower())
        except ImportError:
            results['coingecko'] = False

        # Basic format validation
        results['format_valid'] = self.is_valid_symbol(normalized)

        return results






# Singleton instance
_symbol_service = None
_instance_lock = threading.RLock()

def get_symbol_service() -> SymbolService:
    """Lấy instance singleton của SymbolService."""
    global _symbol_service
    with _instance_lock:
        if _symbol_service is None:
            _symbol_service = SymbolService()
        return _symbol_service

# Các hàm tiện ích để tương thích ngược với mã cũ
def normalize_symbol(symbol: str) -> str:
    """
    Chuẩn hóa symbol thành dạng BTCUSDT.

    Args:
        symbol: Symbol cần chuẩn hóa

    Returns:
        Symbol đã chuẩn hóa
    """
    service = get_symbol_service()
    return service.normalize_symbol(symbol)

def format_symbol_for_exchange(symbol: str) -> str:
    """
    Chuyển đổi symbol từ BTCUSDT sang BTC/USDT cho Futures API.

    Args:
        symbol: Symbol cần chuyển đổi

    Returns:
        Symbol đã chuyển đổi cho Futures
    """
    service = get_symbol_service()
    return service.format_symbol_for_exchange(symbol)

def is_valid_symbol(symbol: str) -> bool:
    """
    Kiểm tra tính hợp lệ của symbol.

    Args:
        symbol: Symbol cần kiểm tra

    Returns:
        True nếu symbol hợp lệ, False nếu không
    """
    service = get_symbol_service()
    return service.is_valid_symbol(symbol)

def smart_normalize_symbol(symbol: str) -> str:
    """
    Smart symbol normalization for chart and price commands.
    - If symbol is a base token (e.g., "btc", "eth"), append "USDT"
    - If symbol is already a trading pair (e.g., "btcusdc", "ethbtc"), use as-is

    Args:
        symbol: Symbol to normalize

    Returns:
        Normalized symbol
    """
    service = get_symbol_service()
    return service.smart_normalize_symbol(symbol)

def get_valid_symbols(symbols: List[str]) -> Tuple[List[str], List[str], List[str]]:
    """
    Lọc và chuẩn hóa danh sách symbols.

    Args:
        symbols: Danh sách symbols cần lọc

    Returns:
        Tuple gồm (valid_symbols, invalid_symbols, formatted_valid_symbols)
    """
    service = get_symbol_service()
    return service.get_valid_symbols(symbols)


