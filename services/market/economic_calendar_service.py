import logging
from datetime import datetime, timezone, timedelta, time
from typing import List, Dict, Any, Optional
from discord.ext import tasks
import discord

from utils.config import load_config
from services.data.cache_service import get_cache_service
from services.core.error_service import handle_service_errors, retry_with_backoff
from services.core.http_client_service import get_http_client

logger = logging.getLogger(__name__)

class EconomicCalendarService:
    def __init__(self, bot):
        self.bot = bot
        config = load_config()

        # Load config
        calendar_config = config.get('economic_calendar', {})
        self.forex_factory_base_url = calendar_config.get('forex_factory_base_url', 'https://nfs.faireconomy.media')
        self.currency_filter = calendar_config.get('currency_filter', ['USD', 'CNY', 'JPY'])

        # Hardcoded values
        self.CHANNEL_NAME = "economic-calendar"
        self.CACHE_TTL = 1800  # 30 minutes
        self.MAX_EVENTS = 15
        self.TIME_RANGE_HOURS = 168  # 7 days
        self.IMPACT_FILTER = ["High"]
        self.DAILY_POST_TIME = "00:00"  # UTC
        self.ALERT_BEFORE_MINUTES = 30

        self.cache_service = get_cache_service()
        self.last_alert_cache = {}  # Track sent alerts to avoid duplicates

        logger.info("Economic Calendar Service initialized")

    async def start_scheduled_tasks(self):
        """Start all scheduled tasks"""
        try:
            if not self.daily_calendar_post.is_running():
                self.daily_calendar_post.start()
                logger.info("Daily calendar post task started")

            if not self.check_upcoming_events.is_running():
                self.check_upcoming_events.start()
                logger.info("Event alert task started")

        except Exception as e:
            logger.error(f"Error starting scheduled tasks: {e}")

    async def stop_scheduled_tasks(self):
        """Stop all scheduled tasks"""
        try:
            if self.daily_calendar_post.is_running():
                self.daily_calendar_post.cancel()

            if self.check_upcoming_events.is_running():
                self.check_upcoming_events.cancel()

            logger.info("Economic calendar scheduled tasks stopped")
        except Exception as e:
            logger.error(f"Error stopping scheduled tasks: {e}")

    async def get_or_create_calendar_channel(self, guild: discord.Guild) -> Optional[discord.TextChannel]:
        """Get or create the economic-calendar channel"""
        try:
            # Find existing channel
            for channel in guild.text_channels:
                if channel.name == self.CHANNEL_NAME:
                    logger.info(f"Found existing calendar channel: #{channel.name} in {guild.name}")
                    return channel

            # Create new channel if bot has permission
            if guild.me.guild_permissions.manage_channels:
                channel = await guild.create_text_channel(
                    name=self.CHANNEL_NAME,
                    topic="📅 Economic Calendar & High Impact Market Events • Auto-updated daily at 00:00 UTC",
                    reason="Auto-created by ChartFix bot for economic calendar"
                )

                # Send welcome message
                welcome_embed = discord.Embed(
                    title="📅 Welcome to Economic Calendar!",
                    description="This channel provides automated economic calendar updates.\n\n"
                              "**Features:**\n"
                              "📊 Daily calendar posts at 00:00 UTC\n"
                              "🔥 High impact events only\n"
                              "⚡ 30-minute pre-event alerts\n"
                              "💱 USD, CNY, JPY currencies\n"
                              "🎯 Crypto market impact analysis",
                    color=0x3498db,
                    timestamp=discord.utils.utcnow()
                )
                welcome_embed.set_footer(text="ChartFix Economic Calendar System")
                await channel.send(embed=welcome_embed)

                logger.info(f"Created new calendar channel: #{channel.name} in {guild.name}")
                return channel
            else:
                logger.warning(f"Bot lacks permission to create channels in {guild.name}")
                return None

        except Exception as e:
            logger.error(f"Error getting/creating calendar channel: {e}")
            return None

    @handle_service_errors
    @retry_with_backoff(max_retries=2)
    async def fetch_economic_calendar(self) -> List[Dict[str, Any]]:
        """Fetch economic calendar events from Forex Factory"""
        cache_key = "economic_calendar_events"

        cached_data = self.cache_service.get(cache_key)
        if cached_data:
            logger.info("Returning cached economic calendar data")
            return cached_data

        try:
            url = f"{self.forex_factory_base_url}/ff_calendar_thisweek.json"

            http_client = await get_http_client()
            async with http_client.request('GET', url, timeout=15) as response:
                if response.status == 200:
                    events = await response.json()

                    # Filter events
                    filtered_events = self._filter_calendar_events(events)

                    # Cache results
                    self.cache_service.set(cache_key, filtered_events, ttl=self.CACHE_TTL)
                    logger.info(f"Fetched {len(filtered_events)} economic calendar events")
                    return filtered_events
                else:
                    logger.error(f"Forex Factory API error: {response.status}")
                    return []

        except Exception as e:
            logger.error(f"Error fetching economic calendar: {e}")
            return []

    def _filter_calendar_events(self, events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter and sort economic calendar events"""
        try:
            now = datetime.now(timezone.utc)
            cutoff = now + timedelta(hours=self.TIME_RANGE_HOURS)

            filtered = []
            for event in events:
                # Filter by impact (hardcoded to High only)
                if event.get('impact') not in self.IMPACT_FILTER:
                    continue

                # Filter by currency
                if event.get('country') not in self.currency_filter:
                    continue

                # Filter by time range
                try:
                    event_time = datetime.fromisoformat(event['date'].replace('Z', '+00:00'))
                    if not (now <= event_time <= cutoff):
                        continue
                except:
                    continue

                filtered.append(event)

            # Sort by date
            filtered.sort(key=lambda x: x.get('date', ''))

            # Limit results
            return filtered[:self.MAX_EVENTS]

        except Exception as e:
            logger.error(f"Error filtering calendar events: {e}")
            return []

    @tasks.loop(time=time(hour=0, minute=0))  # 00:00 UTC daily
    async def daily_calendar_post(self):
        """Auto-post daily economic calendar"""
        try:
            logger.info("Starting daily calendar post...")

            for guild in self.bot.guilds:
                try:
                    calendar_channel = await self.get_or_create_calendar_channel(guild)
                    if calendar_channel:
                        events = await self.fetch_economic_calendar()
                        embed = self.create_daily_calendar_embed(events)
                        await calendar_channel.send(embed=embed)
                        logger.info(f"Posted daily calendar to {guild.name}")
                    else:
                        logger.warning(f"Could not find/create calendar channel in {guild.name}")

                except Exception as e:
                    logger.error(f"Error posting daily calendar to {guild.name}: {e}")

        except Exception as e:
            logger.error(f"Error in daily calendar post task: {e}")

    @tasks.loop(minutes=30)  # Check every 30 minutes
    async def check_upcoming_events(self):
        """Check for upcoming events and send alerts"""
        try:
            events = await self.fetch_economic_calendar()
            now = datetime.now(timezone.utc)

            for event in events:
                try:
                    event_time = datetime.fromisoformat(event['date'].replace('Z', '+00:00'))
                    time_diff_minutes = (event_time - now).total_seconds() / 60

                    # Alert 25-35 minutes before (30 ± 5 minutes window)
                    if 25 <= time_diff_minutes <= 35 and event.get('impact') == 'High':
                        event_key = f"{event.get('title', '')}_{event.get('date', '')}"

                        # Check if we already sent alert for this event
                        if event_key not in self.last_alert_cache:
                            await self.send_event_alert(event, time_diff_minutes)
                            self.last_alert_cache[event_key] = now

                except Exception as e:
                    logger.error(f"Error processing event alert: {e}")

        except Exception as e:
            logger.error(f"Error in event alert task: {e}")

    async def send_event_alert(self, event: Dict[str, Any], minutes_until: float):
        """Send alert for upcoming high impact event"""
        try:
            for guild in self.bot.guilds:
                calendar_channel = await self.get_or_create_calendar_channel(guild)
                if calendar_channel:
                    embed = self.create_event_alert_embed(event, minutes_until)
                    await calendar_channel.send(embed=embed)
                    logger.info(f"Sent event alert for {event.get('title', 'Unknown')} to {guild.name}")

        except Exception as e:
            logger.error(f"Error sending event alert: {e}")

    def create_daily_calendar_embed(self, events: List[Dict[str, Any]]) -> discord.Embed:
        """Create daily calendar embed"""
        try:
            title = "📅 ECONOMIC CALENDAR - Next 7 Days"

            if not events:
                description = "📅 No high-impact economic events scheduled for the next 7 days."
                color = 0x95a5a6
            else:
                lines = ["```"]
                lines.append("DATE   | CUR | EVENT")
                lines.append("=" * 40)

                for event in events:
                    time_str = self._format_event_time(event['date'])
                    country = event.get('country', 'N/A')[:3]
                    title_text = event.get('title', 'Unknown Event')

                    # Truncate long titles
                    if len(title_text) > 28:
                        title_text = title_text[:25] + "..."

                    impact_symbol = "🔥"  # Only High impact events
                    line = f"{time_str} | {country} | {impact_symbol} {title_text}"
                    lines.append(line)

                    # Add forecast/previous if available
                    forecast = event.get('forecast', '')
                    previous = event.get('previous', '')
                    if forecast or previous:
                        forecast_display = forecast if forecast else 'N/A'
                        previous_display = previous if previous else 'N/A'
                        lines.append(f"       📈 F: {forecast_display} | P: {previous_display}")

                lines.append("```")
                description = "\n".join(lines)
                color = 0x3498db

            # Convert UTC to VN time (+7) and remove UTC
            utc_time = datetime.now(timezone.utc)
            vn_time = utc_time.replace(hour=(utc_time.hour + 7) % 24)
            timestamp = vn_time.strftime('%H:%M')
            footer = f"Economic Calendar • Updated: {timestamp} • Auto-posted daily at 07:00"

            embed = discord.Embed(
                title=title,
                description=description,
                color=color,
                timestamp=discord.utils.utcnow()
            )

            embed.set_footer(text=footer)
            embed.add_field(
                name="⚡ Alert System",
                value="You'll receive alerts 30 minutes before High impact events",
                inline=False
            )

            return embed

        except Exception as e:
            logger.error(f"Error creating daily calendar embed: {e}")
            return discord.Embed(
                title="❌ Error",
                description="Unable to create economic calendar",
                color=0xe74c3c
            )

    def create_event_alert_embed(self, event: Dict[str, Any], minutes_until: float) -> discord.Embed:
        """Create event alert embed"""
        try:
            title = "🚨 ECONOMIC EVENT ALERT"

            event_title = event.get('title', 'Unknown Event')
            country = event.get('country', 'N/A')
            forecast = event.get('forecast', '')
            previous = event.get('previous', '')

            description = f"🔥 **{event_title}**\n"
            description += f"🌍 **Currency:** {country}\n"
            description += f"⏰ **Starting in:** {int(minutes_until)} minutes\n"
            description += f"💥 **Impact Level:** HIGH\n\n"

            if forecast or previous:
                description += "📊 **Market Data:**\n"
                if forecast:
                    description += f"📈 Forecast: {forecast}\n"
                if previous:
                    description += f"📋 Previous: {previous}\n"
                description += "\n"

            # Add crypto impact analysis
            crypto_impact = self._get_crypto_impact_analysis(event_title, country)
            description += f"🎯 **Crypto Impact:** {crypto_impact}\n"
            description += "⚠️ **Trader Alert:** Consider risk management before event"

            embed = discord.Embed(
                title=title,
                description=description,
                color=0xe67e22,  # Orange for alerts
                timestamp=discord.utils.utcnow()
            )

            embed.set_footer(text="ChartFix Economic Alert System")

            return embed

        except Exception as e:
            logger.error(f"Error creating event alert embed: {e}")
            return discord.Embed(
                title="🚨 Event Alert",
                description=f"High impact event: {event.get('title', 'Unknown')} starting soon",
                color=0xe67e22
            )

    def create_quick_calendar_embed(self, events: List[Dict[str, Any]], channel_mention: str) -> discord.Embed:
        """Create quick calendar embed for /calendar command"""
        try:
            next_events = events[:3] if events else []

            description = f"📍 **Full calendar:** {channel_mention}\n"
            description += f"🔄 **Auto-updated:** Daily at 07:00\n"
            description += f"⚡ **Real-time alerts:** 30 min before High impact events\n\n"

            if next_events:
                description += "🔥 **Next High Impact Events:**\n"
                for event in next_events:
                    time_str = self._format_event_time(event['date'])
                    country = event.get('country', 'N/A')
                    title = event.get('title', 'Unknown')[:25]
                    description += f"• {time_str} | {country} | {title}\n"
            else:
                description += "📅 No high impact events in next 24 hours"

            embed = discord.Embed(
                title="📅 Economic Calendar",
                description=description,
                color=0x3498db,
                timestamp=discord.utils.utcnow()
            )

            embed.set_footer(text="Use the dedicated channel for full details")

            return embed

        except Exception as e:
            logger.error(f"Error creating quick calendar embed: {e}")
            return discord.Embed(
                title="📅 Economic Calendar",
                description=f"Check {channel_mention} for economic calendar",
                color=0x3498db
            )

    def _format_event_time(self, date_str: str) -> str:
        """Format event time for display - Convert UTC to VN time (+7)"""
        try:
            dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            # Convert UTC to VN time (+7) and remove UTC
            vn_dt = dt.replace(hour=(dt.hour + 7) % 24)
            return vn_dt.strftime("%m/%d %H:%M")
        except:
            return "N/A"

    def _get_crypto_impact_analysis(self, event_title: str, country: str) -> str:
        """Get crypto impact analysis for event"""
        event_lower = event_title.lower()

        if 'fomc' in event_lower or 'fed' in event_lower or 'interest rate' in event_lower:
            return "Very High - Fed decisions directly impact BTC/crypto markets"
        elif 'cpi' in event_lower or 'inflation' in event_lower:
            return "High - Inflation data affects risk assets including crypto"
        elif 'gdp' in event_lower:
            return "Medium-High - Economic growth impacts market sentiment"
        elif 'unemployment' in event_lower or 'jobs' in event_lower:
            return "Medium - Employment data affects USD strength"
        elif country == 'CNY':
            return "Medium - China economic data impacts global risk sentiment"
        elif country == 'JPY':
            return "Low-Medium - Yen movements can affect carry trades"
        else:
            return "Medium - Monitor for unexpected market reactions"

# Global instance
_economic_calendar_service = None

def get_economic_calendar_service(bot=None):
    global _economic_calendar_service
    if _economic_calendar_service is None and bot:
        _economic_calendar_service = EconomicCalendarService(bot)
    return _economic_calendar_service
