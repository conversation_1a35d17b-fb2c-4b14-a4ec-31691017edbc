import logging
import discord
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

class DiscordService:
    def __init__(self, bot):
        self.bot = bot

    def create_embed(
        self,
        title: str,
        description: str = None,
        color: int = 0x00ff88,
        fields: List[Dict[str, Any]] = None,
        footer: str = None,
        thumbnail: str = None,
        timestamp: bool = True
    ) -> discord.Embed:
        embed = discord.Embed(
            title=title,
            description=description,
            color=color,
            timestamp=discord.utils.utcnow() if timestamp else None
        )

        if fields:
            for field in fields:
                embed.add_field(
                    name=field.get('name', ''),
                    value=field.get('value', ''),
                    inline=field.get('inline', True)
                )

        if footer:
            embed.set_footer(text=footer)

        if thumbnail:
            embed.set_thumbnail(url=thumbnail)

        return embed

    def create_error_embed(self, message: str) -> discord.Embed:
        return self.create_embed(
            title="❌ Lỗi",
            description=message,
            color=0xff4444
        )

    def create_success_embed(self, message: str) -> discord.Embed:
        return self.create_embed(
            title="✅ Thành công",
            description=message,
            color=0x00ff88
        )

    def create_info_embed(self, title: str, message: str) -> discord.Embed:
        return self.create_embed(
            title=f"ℹ️ {title}",
            description=message,
            color=0x3498db
        )

    def format_utc_time(self, dt: datetime = None) -> str:
        if dt is None:
            dt = datetime.utcnow()

        # Convert UTC to VN time (+7) and remove UTC
        vn_dt = dt.replace(hour=(dt.hour + 7) % 24)
        return vn_dt.strftime('%Y-%m-%d %H:%M:%S')

    def format_price(self, price: float) -> str:
        if price < 0.1:
            return f"${price:.6f}"
        elif price < 1:
            return f"${price:.4f}"
        elif price < 100:
            return f"${price:.2f}"
        else:
            return f"${price:,.0f}"

    def format_volume(self, volume: float) -> str:
        if volume >= 1_000_000_000:
            return f"{volume/1_000_000_000:.1f}B"
        elif volume >= 1_000_000:
            return f"{volume/1_000_000:.0f}M"
        elif volume >= 1_000:
            return f"{volume/1_000:.0f}K"
        else:
            return f"{volume:.0f}"

    def format_percentage(self, percentage: float, show_sign: bool = True) -> str:
        if percentage > 0:
            emoji = "🟢"
            sign = "+" if show_sign else ""
        elif percentage < 0:
            emoji = "🔴"
            sign = ""
        else:
            emoji = "⚪"
            sign = ""

        return f"{emoji}{sign}{percentage:.2f}%"

    def get_crypto_thumbnail(self, symbol: str = "BTC") -> str:
        logos = {
            "BTC": "https://cryptologos.cc/logos/bitcoin-btc-logo.png",
            "ETH": "https://cryptologos.cc/logos/ethereum-eth-logo.png",
            "BNB": "https://cryptologos.cc/logos/binance-coin-bnb-logo.png",
            "SOL": "https://cryptologos.cc/logos/solana-sol-logo.png",
            "USDT": "https://cryptologos.cc/logos/tether-usdt-logo.png",
        }

        return logos.get(symbol.upper(), logos["BTC"])

    async def send_dm(self, user_id: int, embed: discord.Embed) -> bool:
        try:
            user = await self.bot.fetch_user(user_id)
            await user.send(embed=embed)
            return True
        except Exception as e:
            logger.error(f"Failed to send DM to user {user_id}: {e}")
            return False

    def generate_invite_link(self) -> str:
        if not self.bot.user:
            return "Bot not ready"

        permissions = discord.Permissions(
            send_messages=True,
            use_slash_commands=True,
            embed_links=True,
            read_message_history=True,
            manage_messages=True,
            manage_channels=True
        )

        return discord.utils.oauth_url(
            self.bot.user.id,
            permissions=permissions,
            scopes=('bot', 'applications.commands')
        )


class BotStats:
    def __init__(self):
        self.start_time = datetime.utcnow()
        self.command_count = defaultdict(int)
        self.error_count = defaultdict(int)
        self.response_times = defaultdict(deque)
        self.api_calls = defaultdict(int)

        self.max_response_times = 100

    def record_command(self, command_name: str):
        self.command_count[command_name] += 1

    def record_error(self, error_type: str):
        self.error_count[error_type] += 1

    def record_response_time(self, command_name: str, response_time: float):
        times = self.response_times[command_name]
        times.append(response_time)

        if len(times) > self.max_response_times:
            times.popleft()

    def record_api_call(self, api_name: str):
        self.api_calls[api_name] += 1

    def get_uptime(self) -> timedelta:
        return datetime.utcnow() - self.start_time

    def get_avg_response_time(self, command_name: str) -> float:
        times = self.response_times.get(command_name, [])
        return sum(times) / len(times) if times else 0.0

    def get_total_commands(self) -> int:
        return sum(self.command_count.values())

    def get_total_errors(self) -> int:
        return sum(self.error_count.values())

    def get_stats_summary(self) -> Dict[str, Any]:
        return {
            'uptime': self.get_uptime(),
            'total_commands': self.get_total_commands(),
            'total_errors': self.get_total_errors(),
            'command_count': dict(self.command_count),
            'error_count': dict(self.error_count),
            'api_calls': dict(self.api_calls),
            'avg_response_times': {
                cmd: self.get_avg_response_time(cmd)
                for cmd in self.command_count.keys()
            }
        }

def get_discord_service(bot) -> DiscordService:
    return DiscordService(bot)
