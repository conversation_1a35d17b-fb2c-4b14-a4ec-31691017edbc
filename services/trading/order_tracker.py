#!/usr/bin/env python3
"""
Order Tracking System for Binance Futures Trading Bot
Tracks order status, fills, and triggers notifications
"""

import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from services.data.database import trading_db
from services.trading.trading_service import BinanceFuturesTrading

logger = logging.getLogger(__name__)

class OrderTracker:
    def __init__(self, trading_service: BinanceFuturesTrading = None):
        self.trading_service = trading_service
        self.db = trading_db

    def track_order(self, order_result: Dict, user_id: str, metadata: Dict = None) -> bool:
        """
        Start tracking an order after it's placed

        Args:
            order_result: Result from trading service place_order
            user_id: Discord user ID
            metadata: Additional data (command, channel, etc.)
        """
        try:
            if not order_result.get('success'):
                logger.error(f"Cannot track failed order: {order_result.get('error')}")
                return False

            order_data = {
                'order_id': order_result['order_id'],
                'user_id': user_id,
                'symbol': order_result.get('symbol', ''),
                'side': order_result.get('side', ''),
                'type': order_result.get('type', 'limit'),
                'amount': order_result.get('amount', 0.0),
                'price': order_result.get('price', 0.0),
                'status': 'pending',
                'position_side': order_result.get('position_side', ''),
                'created_at': datetime.now(timezone.utc).isoformat(),
                'tp_order_id': order_result.get('tp_order_id'),
                'sl_order_id': order_result.get('sl_order_id'),
                'strategy': metadata.get('strategy', 'manual') if metadata else 'manual',
                'metadata': metadata or {}
            }

            success = self.db.create_order(order_data)
            if success:
                logger.info(f"✅ Started tracking order: {order_data['order_id']}")

                # Also track TP/SL orders if they exist
                if order_result.get('tp_order_id'):
                    self._track_tp_sl_order(order_result['tp_order_id'], user_id, 'take_profit', metadata)

                if order_result.get('sl_order_id'):
                    self._track_tp_sl_order(order_result['sl_order_id'], user_id, 'stop_loss', metadata)

            return success

        except Exception as e:
            logger.error(f"❌ Failed to track order: {e}")
            return False

    def _track_tp_sl_order(self, order_id: str, user_id: str, order_type: str, metadata: Dict):
        """Track TP/SL orders separately"""
        try:
            tp_sl_data = {
                'order_id': order_id,
                'user_id': user_id,
                'symbol': metadata.get('symbol', ''),
                'side': 'sell' if order_type == 'take_profit' else 'sell',  # Will be corrected based on position
                'type': order_type,
                'amount': metadata.get('amount', 0.0),
                'price': metadata.get('tp_price' if order_type == 'take_profit' else 'sl_price', 0.0),
                'status': 'pending',
                'position_side': metadata.get('position_side', ''),
                'created_at': datetime.now(timezone.utc).isoformat(),
                'strategy': f"{metadata.get('strategy', 'manual')}_{order_type}",
                'metadata': metadata
            }

            self.db.create_order(tp_sl_data)
            logger.info(f"✅ Started tracking {order_type} order: {order_id}")

        except Exception as e:
            logger.error(f"❌ Failed to track {order_type} order: {e}")

    def update_order_status(self, order_id: str, new_status: str, fill_data: Dict = None) -> bool:
        """
        Update order status when status changes

        Args:
            order_id: Binance order ID
            new_status: New status (filled, cancelled, rejected, etc.)
            fill_data: Fill information if order was filled
        """
        try:
            filled_data = None
            if new_status == 'filled' and fill_data:
                filled_data = {
                    'filled_at': datetime.now(timezone.utc).isoformat(),
                    'filled_price': fill_data.get('price', 0.0),
                    'filled_amount': fill_data.get('amount', 0.0)
                }

            success = self.db.update_order_status(order_id, new_status, filled_data)

            if success:
                logger.info(f"✅ Order status updated: {order_id} -> {new_status}")

                # If order was filled, trigger position update
                if new_status == 'filled':
                    self._handle_order_fill(order_id, fill_data)

            return success

        except Exception as e:
            logger.error(f"❌ Failed to update order status: {e}")
            return False

    def _handle_order_fill(self, order_id: str, fill_data: Dict):
        """Handle order fill event - Phase 2 implementation"""
        try:
            # Get order details
            orders = self.db.get_orders()
            order = next((o for o in orders if o['order_id'] == order_id), None)

            if not order:
                logger.error(f"Order not found for fill handling: {order_id}")
                return

            logger.info(f"🎯 Order filled: {order['symbol']} {order['side']} {order['amount']} @ {fill_data.get('price')}")

            # Phase 2: Update position
            from services.trading.position_manager import position_manager
            from services.trading.notification_service import notification_service

            success = position_manager.update_position_from_fill(order, fill_data)

            if success:
                # Get updated position for notification
                position = position_manager.get_position(
                    order['user_id'], order['symbol'], order['position_side']
                )

                if position:
                    # Send position notification (async safe)
                    try:
                        import asyncio
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # If we're in an async context, schedule the task
                            asyncio.create_task(
                                notification_service.send_position_notification(
                                    order['user_id'], 'position_updated', position
                                )
                            )
                        else:
                            # If not in async context, run it
                            loop.run_until_complete(
                                notification_service.send_position_notification(
                                    order['user_id'], 'position_updated', position
                                )
                            )
                    except RuntimeError:
                        # No event loop, skip notification for now
                        logger.warning("⚠️ No event loop available for position notification")
                        pass

        except Exception as e:
            logger.error(f"❌ Failed to handle order fill: {e}")

    def get_user_orders(self, user_id: str, status: str = None) -> List[Dict]:
        """Get orders for a specific user"""
        try:
            orders = self.db.get_orders(user_id=user_id, status=status)
            logger.info(f"📋 Retrieved {len(orders)} orders for user {user_id}")
            return orders
        except Exception as e:
            logger.error(f"❌ Failed to get user orders: {e}")
            return []

    def get_pending_orders(self, user_id: str = None) -> List[Dict]:
        """Get all pending orders"""
        return self.get_user_orders(user_id, status='pending') if user_id else self.db.get_orders(status='pending')

    def check_order_status_from_exchange(self, order_id: str, symbol: str) -> Optional[Dict]:
        """Check order status directly from Binance API"""
        try:
            if not self.trading_service:
                logger.error("Trading service not available")
                return None

            # Use trading service to check order status
            # This would need to be implemented in trading_service
            # For now, return mock data
            logger.info(f"🔍 Checking order status from exchange: {order_id}")

            # TODO: Implement actual API call
            # status = self.trading_service.get_order_status(symbol, order_id)
            # return status

            return None

        except Exception as e:
            logger.error(f"❌ Failed to check order status from exchange: {e}")
            return None

    def cleanup_old_orders(self, days: int = 7) -> int:
        """Clean up old completed orders (older than specified days)"""
        try:
            # This would be implemented to remove old orders from database
            # For now, just log the action
            logger.info(f"🧹 Cleaning up orders older than {days} days")
            return 0
        except Exception as e:
            logger.error(f"❌ Failed to cleanup old orders: {e}")
            return 0

    def get_order_summary(self, user_id: str) -> Dict:
        """Get order summary for user"""
        try:
            all_orders = self.get_user_orders(user_id)

            summary = {
                'total_orders': len(all_orders),
                'pending': len([o for o in all_orders if o['status'] == 'pending']),
                'filled': len([o for o in all_orders if o['status'] == 'filled']),
                'cancelled': len([o for o in all_orders if o['status'] == 'cancelled']),
                'rejected': len([o for o in all_orders if o['status'] == 'rejected']),
                'recent_orders': all_orders[:5]  # Last 5 orders
            }

            return summary

        except Exception as e:
            logger.error(f"❌ Failed to get order summary: {e}")
            return {}

# Global order tracker instance
order_tracker = OrderTracker()
