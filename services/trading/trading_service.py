import logging
import ccxt
import time
from typing import Dict, Any

from utils.config import get_binance_credentials, load_config
from services.core.error_service import handle_service_errors, retry_with_backoff, NetworkError, TimeoutError
from services.core.symbol_service import format_symbol_for_exchange

logger = logging.getLogger(__name__)

class BinanceFuturesTrading:
    """Binance Futures trading service - optimized for hedge mode"""

    def __init__(self):
        self.exchange = None
        self._initialize_exchange()

    def _initialize_exchange(self):
        """Initialize Binance Futures exchange"""
        try:
            credentials = get_binance_credentials()
            config = load_config()

            api_key = credentials.get('api_key', '')
            api_secret = credentials.get('api_secret', '')
            use_testnet = config.get('binance', {}).get('testnet', False)

            logger.info(f"Initializing Binance exchange with testnet: {use_testnet}")
            logger.info(f"API Key length: {len(api_key) if api_key else 0}")
            logger.info(f"API Secret length: {len(api_secret) if api_secret else 0}")

            if not api_key or not api_secret:
                raise ValueError("Binance API credentials not found in config")

            self.exchange = ccxt.binance({
                'apiKey': api_key,
                'secret': api_secret,
                'timeout': 30000,
                'enableRateLimit': True,
                'sandbox': use_testnet,
                'options': {
                    'defaultType': 'future'
                }
            })

            logger.info("Loading markets...")
            self.exchange.load_markets()
            logger.info(f"Binance Futures initialized successfully (testnet: {use_testnet})")
            logger.info(f"Available markets: {len(self.exchange.markets)}")

        except Exception as e:
            logger.error(f"Failed to initialize Binance Futures: {e}")
            logger.error(f"Error type: {type(e).__name__}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise

    @handle_service_errors
    @retry_with_backoff(max_retries=3, initial_backoff=2.0, max_backoff=10.0,
                       exception_types=[NetworkError, TimeoutError, ccxt.NetworkError, ccxt.ExchangeNotAvailable])
    def get_account_balance(self) -> Dict[str, Any]:
        """Get account balance"""
        try:
            import traceback
            import inspect
            caller_info = inspect.stack()[1]
            logger.info(f"Balance request from: {caller_info.filename}:{caller_info.lineno} in {caller_info.function}")
            logger.debug("Fetching account balance from Binance...")
            balance = self.exchange.fetch_balance()
            logger.debug(f"Balance fetch successful: USDT free={balance.get('USDT', {}).get('free', 0)}")
            return {
                'success': True,
                'free_usdt': balance.get('USDT', {}).get('free', 0),
                'total_usdt': balance.get('USDT', {}).get('total', 0)
            }
        except ccxt.AuthenticationError as e:
            logger.error(f"Binance authentication error: {e}")
            logger.error(f"API Key configured: {bool(self.exchange.apiKey)}")
            logger.error(f"API Secret configured: {bool(self.exchange.secret)}")
            return {'success': False, 'error': f'Authentication failed: {str(e)}'}
        except ccxt.PermissionDenied as e:
            logger.error(f"Binance permission denied: {e}")
            logger.error("Check if API key has futures trading permissions")
            return {'success': False, 'error': f'Permission denied: {str(e)}'}
        except ccxt.DDoSProtection as e:
            logger.warning(f"Rate limit hit when fetching balance: {e}")
            time.sleep(5)
            raise NetworkError(f"Rate limit exceeded: {e}", e)
        except ccxt.NetworkError as e:
            logger.error(f"Network error fetching balance: {e}")
            logger.error(f"Exchange status: {getattr(self.exchange, 'status', 'unknown')}")
            raise NetworkError(f"Network connection issue: {e}", e)
        except ccxt.ExchangeNotAvailable as e:
            logger.error(f"Binance exchange not available: {e}")
            raise NetworkError(f"Exchange unavailable: {e}", e)
        except ccxt.BaseError as e:
            logger.error(f"CCXT base error fetching balance: {e}")
            logger.error(f"Error type: {type(e).__name__}")
            return {'success': False, 'error': f'Exchange error: {str(e)}'}
        except Exception as e:
            logger.error(f"Unexpected error fetching balance: {e}")
            logger.error(f"Error type: {type(e).__name__}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {'success': False, 'error': f'Unexpected error: {str(e)}'}

    @handle_service_errors
    def place_market_order(self, symbol: str, side: str, amount: float,
                          position_side: str = None) -> Dict[str, Any]:
        """
        Place market order (hedge mode optimized)

        Args:
            symbol: Trading symbol
            side: 'buy' or 'sell'
            amount: Amount to trade
            position_side: 'LONG' for buy, 'SHORT' for sell (auto-set if None)
        """
        try:
            if position_side is None:
                position_side = 'LONG' if side == 'buy' else 'SHORT'

            formatted_symbol = format_symbol_for_exchange(symbol)

            order = self.exchange.create_market_order(
                symbol=formatted_symbol,
                side=side,
                amount=amount,
                params={'positionSide': position_side}
            )

            logger.info(f"Market {side}: {amount} {symbol} (position: {position_side})")
            return {
                'success': True,
                'order_id': order.get('id'),
                'status': order.get('status')
            }

        except Exception as e:
            logger.error(f"Market order error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def place_limit_order(self, symbol: str, side: str, amount: float, price: float,
                         position_side: str = None) -> Dict[str, Any]:
        """
        Place limit order (hedge mode optimized)

        Args:
            symbol: Trading symbol
            side: 'buy' or 'sell'
            amount: Amount to trade
            price: Limit price
            position_side: 'LONG' for buy, 'SHORT' for sell (auto-set if None)
        """
        try:
            if position_side is None:
                position_side = 'LONG' if side == 'buy' else 'SHORT'

            formatted_symbol = format_symbol_for_exchange(symbol)

            order = self.exchange.create_limit_order(
                symbol=formatted_symbol,
                side=side,
                amount=amount,
                price=price,
                params={'positionSide': position_side}
            )

            logger.info(f"Limit {side}: {amount} {symbol} @ {price} (position: {position_side})")
            return {
                'success': True,
                'order_id': order.get('id'),
                'status': order.get('status')
            }

        except Exception as e:
            logger.error(f"Limit order error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def place_stop_loss_order(self, symbol: str, side: str, amount: float, stop_price: float,
                             position_side: str = None, reduce_only: bool = False) -> Dict[str, Any]:
        """
        Place stop loss order (hedge mode optimized)

        Args:
            symbol: Trading symbol
            side: 'buy' or 'sell'
            amount: Amount to trade
            stop_price: Stop price to trigger
            position_side: 'LONG' for buy, 'SHORT' for sell (auto-set if None)
            reduce_only: Whether to reduce existing position only
        """
        try:
            if position_side is None:
                position_side = 'LONG' if side == 'buy' else 'SHORT'

            formatted_symbol = format_symbol_for_exchange(symbol)

            params = {
                'stopPrice': stop_price,
                'positionSide': position_side
            }
            if reduce_only:
                params['reduceOnly'] = True

            order = self.exchange.create_order(
                symbol=formatted_symbol,
                type='STOP_MARKET',
                side=side,
                amount=amount,
                params=params
            )

            logger.info(f"Stop loss: {side} {amount} {symbol} @ {stop_price} (position: {position_side})")
            return {
                'success': True,
                'order_id': order.get('id'),
                'status': order.get('status')
            }

        except Exception as e:
            logger.error(f"Stop loss error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def place_take_profit_order(self, symbol: str, side: str, amount: float, stop_price: float,
                               position_side: str = None, reduce_only: bool = False) -> Dict[str, Any]:
        """
        Place take profit order (hedge mode optimized)

        Args:
            symbol: Trading symbol
            side: 'buy' or 'sell'
            amount: Amount to trade
            stop_price: Price to trigger take profit
            position_side: 'LONG' for buy, 'SHORT' for sell (auto-set if None)
            reduce_only: Whether to reduce existing position only (ignored for take profit)
        """
        try:
            if position_side is None:
                position_side = 'LONG' if side == 'buy' else 'SHORT'

            formatted_symbol = format_symbol_for_exchange(symbol)

            params = {
                'stopPrice': stop_price,
                'positionSide': position_side
            }

            order = self.exchange.create_order(
                symbol=formatted_symbol,
                type='TAKE_PROFIT_MARKET',
                side=side,
                amount=amount,
                params=params
            )

            logger.info(f"Take profit: {side} {amount} {symbol} @ {stop_price} (position: {position_side})")
            return {
                'success': True,
                'order_id': order.get('id'),
                'status': order.get('status')
            }

        except Exception as e:
            logger.error(f"Take profit error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def cancel_order(self, symbol: str, order_id: str) -> Dict[str, Any]:
        """Cancel order"""
        try:
            formatted_symbol = format_symbol_for_exchange(symbol)
            self.exchange.cancel_order(order_id, formatted_symbol)

            logger.info(f"Cancelled order: {order_id}")
            return {'success': True}

        except Exception as e:
            logger.error(f"Cancel order error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def get_open_orders(self, symbol: str = None) -> Dict[str, Any]:
        """Get open orders"""
        try:
            formatted_symbol = format_symbol_for_exchange(symbol) if symbol else None
            orders = self.exchange.fetch_open_orders(formatted_symbol)

            return {
                'success': True,
                'orders': orders,
                'count': len(orders)
            }

        except Exception as e:
            logger.error(f"Get orders error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def get_order_status(self, symbol: str, order_id: str) -> Dict[str, Any]:
        """Get order status"""
        try:
            formatted_symbol = format_symbol_for_exchange(symbol)
            order = self.exchange.fetch_order(order_id, formatted_symbol)

            return {
                'success': True,
                'status': order.get('status'),
                'filled': order.get('filled', 0)
            }

        except Exception as e:
            logger.error(f"Get order status error: {e}")
            return {'success': False, 'error': str(e)}
