#!/usr/bin/env python3
"""
Risk Management System for Trading Bot Phase 3
Monitors margin levels, position limits, P&L thresholds, and trading risks
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from services.data.database import trading_db
from services.trading.position_manager import position_manager
from services.trading.notification_service import notification_service

logger = logging.getLogger(__name__)

@dataclass
class RiskLimits:
    """Risk management limits configuration"""
    # Margin levels (percentages)
    margin_warning_level: float = 50.0  # Warning at 50%
    margin_danger_level: float = 30.0   # Danger at 30%
    margin_critical_level: float = 10.0  # Critical at 10%

    # Position limits
    max_position_size_per_symbol: float = 1000.0  # USD
    max_total_portfolio_value: float = 10000.0     # USD
    max_open_positions: int = 10

    # Daily limits
    daily_loss_limit: float = 500.0  # USD
    daily_trade_limit: int = 50

    # P&L milestone thresholds (percentages)
    pnl_milestone_thresholds: List[float] = None

    def __post_init__(self):
        if self.pnl_milestone_thresholds is None:
            self.pnl_milestone_thresholds = [5.0, 10.0, 20.0, -5.0, -10.0, -20.0]

class RiskManager:
    def __init__(self):
        self.db = trading_db
        self.default_limits = RiskLimits()

    def get_user_risk_limits(self, user_id: str) -> RiskLimits:
        """Get risk limits for user (with defaults)"""
        try:
            user = self.db.get_user(user_id)
            if user and user.get('settings', {}).get('risk_limits'):
                # Load custom risk limits from user settings
                custom_limits = user['settings']['risk_limits']
                return RiskLimits(**custom_limits)
            else:
                return self.default_limits
        except Exception as e:
            logger.error(f"Error getting user risk limits: {e}")
            return self.default_limits

    def check_margin_level(self, user_id: str, current_balance: float,
                          margin_used: float) -> Dict:
        """Check margin level and return risk assessment"""
        try:
            if margin_used == 0:
                return {
                    'status': 'safe',
                    'margin_level': 100.0,
                    'message': 'No margin used',
                    'action_required': False
                }

            margin_level = (current_balance / margin_used) * 100
            limits = self.get_user_risk_limits(user_id)

            if margin_level <= limits.margin_critical_level:
                status = 'critical'
                message = f'CRITICAL: Margin level at {margin_level:.1f}%'
                action_required = True
            elif margin_level <= limits.margin_danger_level:
                status = 'danger'
                message = f'DANGER: Margin level at {margin_level:.1f}%'
                action_required = True
            elif margin_level <= limits.margin_warning_level:
                status = 'warning'
                message = f'WARNING: Margin level at {margin_level:.1f}%'
                action_required = False
            else:
                status = 'safe'
                message = f'Margin level healthy at {margin_level:.1f}%'
                action_required = False

            return {
                'status': status,
                'margin_level': margin_level,
                'message': message,
                'action_required': action_required,
                'thresholds': {
                    'warning': limits.margin_warning_level,
                    'danger': limits.margin_danger_level,
                    'critical': limits.margin_critical_level
                }
            }

        except Exception as e:
            logger.error(f"Error checking margin level: {e}")
            return {
                'status': 'error',
                'margin_level': 0.0,
                'message': f'Error calculating margin level: {str(e)}',
                'action_required': True
            }

    def check_position_limits(self, user_id: str, symbol: str,
                            new_position_value: float) -> Dict:
        """Check if new position would exceed limits"""
        try:
            limits = self.get_user_risk_limits(user_id)

            # Get current positions
            open_positions = position_manager.get_user_positions(user_id, status='open')

            # Check max open positions
            if len(open_positions) >= limits.max_open_positions:
                return {
                    'allowed': False,
                    'reason': f'Maximum open positions ({limits.max_open_positions}) reached',
                    'current_count': len(open_positions),
                    'limit': limits.max_open_positions
                }

            # Check position size per symbol
            symbol_positions = [p for p in open_positions if p.get('symbol') == symbol]
            current_symbol_value = sum(
                abs(p.get('size', 0)) * p.get('entry_price', 0)
                for p in symbol_positions
            )

            if current_symbol_value + new_position_value > limits.max_position_size_per_symbol:
                return {
                    'allowed': False,
                    'reason': f'Position size limit for {symbol} exceeded',
                    'current_value': current_symbol_value,
                    'new_value': new_position_value,
                    'total_value': current_symbol_value + new_position_value,
                    'limit': limits.max_position_size_per_symbol
                }

            # Check total portfolio value
            total_portfolio_value = sum(
                abs(p.get('size', 0)) * p.get('entry_price', 0)
                for p in open_positions
            )

            if total_portfolio_value + new_position_value > limits.max_total_portfolio_value:
                return {
                    'allowed': False,
                    'reason': 'Total portfolio limit exceeded',
                    'current_value': total_portfolio_value,
                    'new_value': new_position_value,
                    'total_value': total_portfolio_value + new_position_value,
                    'limit': limits.max_total_portfolio_value
                }

            return {
                'allowed': True,
                'reason': 'Position within limits',
                'current_positions': len(open_positions),
                'symbol_value': current_symbol_value,
                'portfolio_value': total_portfolio_value
            }

        except Exception as e:
            logger.error(f"Error checking position limits: {e}")
            return {
                'allowed': False,
                'reason': f'Error checking limits: {str(e)}'
            }

    def check_daily_limits(self, user_id: str) -> Dict:
        """Check daily trading limits"""
        try:
            limits = self.get_user_risk_limits(user_id)
            today = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)

            # Get today's closed positions (realized P&L)
            all_positions = position_manager.get_user_positions(user_id)
            today_closed = [
                p for p in all_positions
                if p['status'] == 'closed' and
                datetime.fromisoformat(p.get('updated_at', '').replace('Z', '+00:00')) >= today
            ]

            # Calculate daily P&L
            daily_realized_pnl = sum(p.get('realized_pnl', 0) for p in today_closed)
            daily_trade_count = len(today_closed)

            # Check daily loss limit
            loss_limit_exceeded = daily_realized_pnl < -limits.daily_loss_limit

            # Check daily trade limit
            trade_limit_exceeded = daily_trade_count >= limits.daily_trade_limit

            return {
                'loss_limit_exceeded': loss_limit_exceeded,
                'trade_limit_exceeded': trade_limit_exceeded,
                'daily_pnl': daily_realized_pnl,
                'daily_trades': daily_trade_count,
                'limits': {
                    'daily_loss_limit': limits.daily_loss_limit,
                    'daily_trade_limit': limits.daily_trade_limit
                },
                'trading_suspended': loss_limit_exceeded or trade_limit_exceeded
            }

        except Exception as e:
            logger.error(f"Error checking daily limits: {e}")
            return {
                'loss_limit_exceeded': False,
                'trade_limit_exceeded': False,
                'daily_pnl': 0.0,
                'daily_trades': 0,
                'trading_suspended': False,
                'error': str(e)
            }

    def check_pnl_milestones(self, user_id: str, current_total_pnl: float,
                           previous_total_pnl: float) -> List[Dict]:
        """Check if P&L has crossed milestone thresholds"""
        try:
            limits = self.get_user_risk_limits(user_id)
            milestones_hit = []

            # Get account starting balance (simplified - could be more sophisticated)
            starting_balance = 1000.0  # This should come from user settings or account data

            current_pnl_percent = (current_total_pnl / starting_balance) * 100
            previous_pnl_percent = (previous_total_pnl / starting_balance) * 100

            for threshold in limits.pnl_milestone_thresholds:
                # Check if we crossed the threshold
                if threshold > 0:  # Profit milestone
                    if previous_pnl_percent < threshold <= current_pnl_percent:
                        milestones_hit.append({
                            'type': 'profit_milestone',
                            'threshold': threshold,
                            'current_pnl': current_total_pnl,
                            'current_percent': current_pnl_percent,
                            'message': f'🎉 Profit milestone reached: +{threshold}%'
                        })
                else:  # Loss milestone
                    if previous_pnl_percent > threshold >= current_pnl_percent:
                        milestones_hit.append({
                            'type': 'loss_milestone',
                            'threshold': threshold,
                            'current_pnl': current_total_pnl,
                            'current_percent': current_pnl_percent,
                            'message': f'⚠️ Loss milestone hit: {threshold}%'
                        })

            return milestones_hit

        except Exception as e:
            logger.error(f"Error checking P&L milestones: {e}")
            return []

    async def send_risk_alert(self, user_id: str, alert_type: str,
                            alert_data: Dict, channel_id: str = None):
        """Send risk management alert"""
        try:
            # Determine alert severity and emoji
            severity_map = {
                'margin_warning': ('⚠️', 0xffa500),
                'margin_danger': ('🚨', 0xff6b35),
                'margin_critical': ('🔴', 0xff0000),
                'position_limit': ('📊', 0xff6b35),
                'daily_limit': ('🛑', 0xff0000),
                'pnl_milestone': ('🎯', 0x00ff88)
            }

            emoji, color = severity_map.get(alert_type, ('⚠️', 0xffa500))

            # Send alert through notification service
            await notification_service.send_alert(
                user_id=user_id,
                alert_type=alert_type,
                message=f"{emoji} {alert_data.get('message', 'Risk alert triggered')}",
                data=alert_data,
                channel_id=channel_id
            )

            logger.info(f"Risk alert sent: {alert_type} for user {user_id}")

        except Exception as e:
            logger.error(f"Error sending risk alert: {e}")

    def get_risk_summary(self, user_id: str) -> Dict:
        """Get comprehensive risk summary for user"""
        try:
            # Get user positions and calculate metrics
            open_positions = position_manager.get_user_positions(user_id, status='open')

            total_margin_used = sum(p.get('margin_used', 0) for p in open_positions)
            total_unrealized_pnl = sum(p.get('unrealized_pnl', 0) for p in open_positions)

            # Simplified balance calculation (in real implementation, get from exchange)
            estimated_balance = 1000.0 + total_unrealized_pnl

            # Check all risk factors
            margin_check = self.check_margin_level(user_id, estimated_balance, total_margin_used)
            daily_limits = self.check_daily_limits(user_id)

            return {
                'user_id': user_id,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'margin_status': margin_check,
                'daily_limits': daily_limits,
                'open_positions': len(open_positions),
                'total_margin_used': total_margin_used,
                'total_unrealized_pnl': total_unrealized_pnl,
                'estimated_balance': estimated_balance,
                'overall_risk_level': self._calculate_overall_risk_level(margin_check, daily_limits)
            }

        except Exception as e:
            logger.error(f"Error getting risk summary: {e}")
            return {
                'user_id': user_id,
                'error': str(e),
                'overall_risk_level': 'unknown'
            }

    def _calculate_overall_risk_level(self, margin_check: Dict, daily_limits: Dict) -> str:
        """Calculate overall risk level"""
        if margin_check.get('status') == 'critical' or daily_limits.get('trading_suspended'):
            return 'critical'
        elif margin_check.get('status') == 'danger':
            return 'high'
        elif margin_check.get('status') == 'warning':
            return 'medium'
        else:
            return 'low'

# Global risk manager instance
risk_manager = RiskManager()
