#!/usr/bin/env python3
"""
Analytics & Performance Tracking Engine for Trading Bot Phase 3
Calculates trading metrics, performance statistics, and risk analytics
"""

import logging
import math
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from services.data.database import trading_db
from services.trading.position_manager import position_manager

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Performance metrics data structure"""
    # Basic metrics
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate: float = 0.0

    # P&L metrics
    total_pnl: float = 0.0
    realized_pnl: float = 0.0
    unrealized_pnl: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    profit_factor: float = 0.0

    # Risk metrics
    max_drawdown: float = 0.0
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0

    # Time metrics
    avg_holding_time: float = 0.0  # in hours
    total_trading_days: int = 0

    # Symbol performance
    best_symbol: str = ""
    worst_symbol: str = ""
    most_traded_symbol: str = ""

class AnalyticsEngine:
    def __init__(self):
        self.db = trading_db

    def calculate_performance_metrics(self, user_id: str,
                                    start_date: Optional[datetime] = None,
                                    end_date: Optional[datetime] = None,
                                    symbol: Optional[str] = None) -> PerformanceMetrics:
        """Calculate comprehensive performance metrics"""
        try:
            # Get positions for analysis
            all_positions = position_manager.get_user_positions(user_id, symbol=symbol)

            # Filter by date range if specified
            if start_date or end_date:
                filtered_positions = []
                for position in all_positions:
                    pos_date = datetime.fromisoformat(position.get('created_at', '').replace('Z', '+00:00'))

                    if start_date and pos_date < start_date:
                        continue
                    if end_date and pos_date > end_date:
                        continue

                    filtered_positions.append(position)

                all_positions = filtered_positions

            # Separate open and closed positions
            open_positions = [p for p in all_positions if p['status'] == 'open']
            closed_positions = [p for p in all_positions if p['status'] == 'closed']

            # Basic metrics
            total_trades = len(closed_positions)
            winning_trades = [p for p in closed_positions if p.get('realized_pnl', 0) > 0]
            losing_trades = [p for p in closed_positions if p.get('realized_pnl', 0) < 0]

            win_rate = (len(winning_trades) / total_trades * 100) if total_trades > 0 else 0.0

            # P&L metrics
            realized_pnl = sum(p.get('realized_pnl', 0) for p in closed_positions)
            unrealized_pnl = sum(p.get('unrealized_pnl', 0) for p in open_positions)
            total_pnl = realized_pnl + unrealized_pnl

            avg_win = sum(p.get('realized_pnl', 0) for p in winning_trades) / len(winning_trades) if winning_trades else 0.0
            avg_loss = sum(p.get('realized_pnl', 0) for p in losing_trades) / len(losing_trades) if losing_trades else 0.0

            # Profit factor
            gross_profit = sum(p.get('realized_pnl', 0) for p in winning_trades)
            gross_loss = abs(sum(p.get('realized_pnl', 0) for p in losing_trades))
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf') if gross_profit > 0 else 0.0

            # Time metrics
            holding_times = []
            for position in closed_positions:
                try:
                    created_at = datetime.fromisoformat(position.get('created_at', '').replace('Z', '+00:00'))
                    updated_at = datetime.fromisoformat(position.get('updated_at', '').replace('Z', '+00:00'))
                    holding_time = (updated_at - created_at).total_seconds() / 3600  # hours
                    holding_times.append(holding_time)
                except:
                    continue

            avg_holding_time = sum(holding_times) / len(holding_times) if holding_times else 0.0

            # Trading days
            if closed_positions:
                first_trade = min(closed_positions, key=lambda x: x.get('created_at', ''))
                last_trade = max(closed_positions, key=lambda x: x.get('updated_at', ''))

                try:
                    first_date = datetime.fromisoformat(first_trade.get('created_at', '').replace('Z', '+00:00'))
                    last_date = datetime.fromisoformat(last_trade.get('updated_at', '').replace('Z', '+00:00'))
                    total_trading_days = (last_date - first_date).days + 1
                except:
                    total_trading_days = 1
            else:
                total_trading_days = 0

            # Symbol performance
            symbol_stats = self._calculate_symbol_performance(all_positions)

            # Risk metrics
            max_drawdown = self._calculate_max_drawdown(closed_positions)
            sharpe_ratio = self._calculate_sharpe_ratio(closed_positions)
            sortino_ratio = self._calculate_sortino_ratio(closed_positions)

            return PerformanceMetrics(
                total_trades=total_trades,
                winning_trades=len(winning_trades),
                losing_trades=len(losing_trades),
                win_rate=win_rate,
                total_pnl=total_pnl,
                realized_pnl=realized_pnl,
                unrealized_pnl=unrealized_pnl,
                avg_win=avg_win,
                avg_loss=avg_loss,
                profit_factor=profit_factor,
                max_drawdown=max_drawdown,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                avg_holding_time=avg_holding_time,
                total_trading_days=total_trading_days,
                best_symbol=symbol_stats.get('best_symbol', ''),
                worst_symbol=symbol_stats.get('worst_symbol', ''),
                most_traded_symbol=symbol_stats.get('most_traded_symbol', '')
            )

        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return PerformanceMetrics()

    def _calculate_symbol_performance(self, positions: List[Dict]) -> Dict:
        """Calculate performance by symbol"""
        try:
            symbol_stats = {}

            for position in positions:
                symbol = position.get('symbol', 'Unknown')
                pnl = position.get('realized_pnl', 0) + position.get('unrealized_pnl', 0)

                if symbol not in symbol_stats:
                    symbol_stats[symbol] = {'pnl': 0.0, 'trades': 0}

                symbol_stats[symbol]['pnl'] += pnl
                symbol_stats[symbol]['trades'] += 1

            if not symbol_stats:
                return {'best_symbol': '', 'worst_symbol': '', 'most_traded_symbol': ''}

            # Best and worst by P&L
            best_symbol = max(symbol_stats.items(), key=lambda x: x[1]['pnl'])[0]
            worst_symbol = min(symbol_stats.items(), key=lambda x: x[1]['pnl'])[0]

            # Most traded by count
            most_traded_symbol = max(symbol_stats.items(), key=lambda x: x[1]['trades'])[0]

            return {
                'best_symbol': best_symbol,
                'worst_symbol': worst_symbol,
                'most_traded_symbol': most_traded_symbol,
                'symbol_stats': symbol_stats
            }

        except Exception as e:
            logger.error(f"Error calculating symbol performance: {e}")
            return {'best_symbol': '', 'worst_symbol': '', 'most_traded_symbol': ''}

    def _calculate_max_drawdown(self, positions: List[Dict]) -> float:
        """Calculate maximum drawdown"""
        try:
            if not positions:
                return 0.0

            # Sort positions by close time
            sorted_positions = sorted(
                positions,
                key=lambda x: datetime.fromisoformat(x.get('updated_at', '').replace('Z', '+00:00'))
            )

            # Calculate running P&L
            running_pnl = 0.0
            peak_pnl = 0.0
            max_drawdown = 0.0

            for position in sorted_positions:
                running_pnl += position.get('realized_pnl', 0)

                if running_pnl > peak_pnl:
                    peak_pnl = running_pnl

                drawdown = peak_pnl - running_pnl
                if drawdown > max_drawdown:
                    max_drawdown = drawdown

            return max_drawdown

        except Exception as e:
            logger.error(f"Error calculating max drawdown: {e}")
            return 0.0

    def _calculate_sharpe_ratio(self, positions: List[Dict], risk_free_rate: float = 0.02) -> float:
        """Calculate Sharpe ratio"""
        try:
            if len(positions) < 2:
                return 0.0

            # Calculate daily returns
            daily_returns = []
            current_date = None
            daily_pnl = 0.0

            for position in sorted(positions, key=lambda x: x.get('updated_at', '')):
                try:
                    pos_date = datetime.fromisoformat(position.get('updated_at', '').replace('Z', '+00:00')).date()

                    if current_date is None:
                        current_date = pos_date

                    if pos_date == current_date:
                        daily_pnl += position.get('realized_pnl', 0)
                    else:
                        if daily_pnl != 0:
                            daily_returns.append(daily_pnl)
                        daily_pnl = position.get('realized_pnl', 0)
                        current_date = pos_date
                except:
                    continue

            # Add last day
            if daily_pnl != 0:
                daily_returns.append(daily_pnl)

            if len(daily_returns) < 2:
                return 0.0

            # Calculate Sharpe ratio
            avg_return = sum(daily_returns) / len(daily_returns)
            std_dev = math.sqrt(sum((r - avg_return) ** 2 for r in daily_returns) / (len(daily_returns) - 1))

            if std_dev == 0:
                return 0.0

            # Annualized Sharpe ratio
            daily_risk_free = risk_free_rate / 365
            sharpe_ratio = (avg_return - daily_risk_free) / std_dev * math.sqrt(365)

            return sharpe_ratio

        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {e}")
            return 0.0

    def _calculate_sortino_ratio(self, positions: List[Dict], risk_free_rate: float = 0.02) -> float:
        """Calculate Sortino ratio (focuses on downside deviation)"""
        try:
            if len(positions) < 2:
                return 0.0

            # Calculate daily returns (same as Sharpe)
            daily_returns = []
            current_date = None
            daily_pnl = 0.0

            for position in sorted(positions, key=lambda x: x.get('updated_at', '')):
                try:
                    pos_date = datetime.fromisoformat(position.get('updated_at', '').replace('Z', '+00:00')).date()

                    if current_date is None:
                        current_date = pos_date

                    if pos_date == current_date:
                        daily_pnl += position.get('realized_pnl', 0)
                    else:
                        if daily_pnl != 0:
                            daily_returns.append(daily_pnl)
                        daily_pnl = position.get('realized_pnl', 0)
                        current_date = pos_date
                except:
                    continue

            if daily_pnl != 0:
                daily_returns.append(daily_pnl)

            if len(daily_returns) < 2:
                return 0.0

            # Calculate downside deviation
            avg_return = sum(daily_returns) / len(daily_returns)
            downside_returns = [r for r in daily_returns if r < avg_return]

            if not downside_returns:
                return float('inf') if avg_return > 0 else 0.0

            downside_deviation = math.sqrt(sum((r - avg_return) ** 2 for r in downside_returns) / len(downside_returns))

            if downside_deviation == 0:
                return 0.0

            # Annualized Sortino ratio
            daily_risk_free = risk_free_rate / 365
            sortino_ratio = (avg_return - daily_risk_free) / downside_deviation * math.sqrt(365)

            return sortino_ratio

        except Exception as e:
            logger.error(f"Error calculating Sortino ratio: {e}")
            return 0.0

    def get_monthly_performance(self, user_id: str, months: int = 12) -> List[Dict]:
        """Get monthly performance breakdown"""
        try:
            monthly_data = []
            current_date = datetime.now(timezone.utc)

            for i in range(months):
                # Calculate month start and end
                month_start = current_date.replace(day=1) - timedelta(days=i*30)
                month_end = month_start + timedelta(days=30)

                # Get metrics for this month
                metrics = self.calculate_performance_metrics(
                    user_id, start_date=month_start, end_date=month_end
                )

                monthly_data.append({
                    'month': month_start.strftime('%Y-%m'),
                    'trades': metrics.total_trades,
                    'pnl': metrics.realized_pnl,
                    'win_rate': metrics.win_rate,
                    'profit_factor': metrics.profit_factor
                })

            return list(reversed(monthly_data))  # Oldest first

        except Exception as e:
            logger.error(f"Error getting monthly performance: {e}")
            return []

    def get_symbol_correlation(self, user_id: str) -> Dict:
        """Calculate correlation between symbol performances"""
        try:
            # This is a simplified correlation analysis
            # In a real implementation, you'd use more sophisticated statistical methods

            positions = position_manager.get_user_positions(user_id)
            symbol_returns = {}

            for position in positions:
                symbol = position.get('symbol', 'Unknown')
                pnl = position.get('realized_pnl', 0) + position.get('unrealized_pnl', 0)

                if symbol not in symbol_returns:
                    symbol_returns[symbol] = []

                symbol_returns[symbol].append(pnl)

            # Calculate basic correlation (simplified)
            correlations = {}
            symbols = list(symbol_returns.keys())

            for i, sym1 in enumerate(symbols):
                for sym2 in symbols[i+1:]:
                    if len(symbol_returns[sym1]) > 1 and len(symbol_returns[sym2]) > 1:
                        # Simplified correlation calculation
                        corr = self._simple_correlation(symbol_returns[sym1], symbol_returns[sym2])
                        correlations[f"{sym1}-{sym2}"] = corr

            return correlations

        except Exception as e:
            logger.error(f"Error calculating symbol correlation: {e}")
            return {}

    def _simple_correlation(self, x: List[float], y: List[float]) -> float:
        """Simple correlation calculation"""
        try:
            if len(x) != len(y) or len(x) < 2:
                return 0.0

            n = len(x)
            sum_x = sum(x)
            sum_y = sum(y)
            sum_xy = sum(x[i] * y[i] for i in range(n))
            sum_x2 = sum(xi ** 2 for xi in x)
            sum_y2 = sum(yi ** 2 for yi in y)

            numerator = n * sum_xy - sum_x * sum_y
            denominator = math.sqrt((n * sum_x2 - sum_x ** 2) * (n * sum_y2 - sum_y ** 2))

            if denominator == 0:
                return 0.0

            return numerator / denominator

        except Exception as e:
            logger.error(f"Error calculating correlation: {e}")
            return 0.0

# Global analytics engine instance
analytics_engine = AnalyticsEngine()
