#!/usr/bin/env python3
"""
Position Management System for Binance Futures Trading Bot
Handles position calculation, P&L tracking, and position lifecycle management
"""

import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple
from decimal import Decimal, ROUND_HALF_UP
from services.data.database import trading_db
from services.trading.trading_service import BinanceFuturesTrading

logger = logging.getLogger(__name__)

class PositionManager:
    def __init__(self, trading_service: BinanceFuturesTrading = None):
        self.trading_service = trading_service
        self.db = trading_db

    def update_position_from_fill(self, order_data: Dict, fill_data: Dict) -> bool:
        """
        Update position when an order is filled

        Args:
            order_data: Order information from database
            fill_data: Fill information (price, amount, etc.)
        """
        try:
            user_id = order_data['user_id']
            symbol = order_data['symbol']
            side = order_data['side']
            position_side = order_data['position_side']
            fill_amount = fill_data['amount']
            fill_price = fill_data['price']

            logger.info(f"🔄 Updating position: {symbol} {position_side} {fill_amount} @ {fill_price}")

            # Get existing position
            existing_position = self.get_position(user_id, symbol, position_side)

            if existing_position:
                # Update existing position
                success = self._update_existing_position(
                    existing_position, side, fill_amount, fill_price, order_data
                )
            else:
                # Create new position
                success = self._create_new_position(
                    user_id, symbol, position_side, side, fill_amount, fill_price, order_data
                )

            if success:
                logger.info(f"✅ Position updated successfully: {symbol} {position_side}")
                return True
            else:
                logger.error(f"❌ Failed to update position: {symbol} {position_side}")
                return False

        except Exception as e:
            logger.error(f"❌ Error updating position from fill: {e}")
            return False

    def _create_new_position(self, user_id: str, symbol: str, position_side: str,
                           side: str, amount: float, price: float, order_data: Dict) -> bool:
        """Create a new position"""
        try:
            position_id = f"pos_{symbol}_{position_side}_{user_id}_{int(datetime.now().timestamp())}"

            # Determine position amount based on side
            if (side == 'buy' and position_side == 'LONG') or (side == 'sell' and position_side == 'SHORT'):
                # Opening position
                position_amount = amount
            else:
                # This shouldn't happen for new positions, but handle it
                logger.warning(f"Unexpected side {side} for new {position_side} position")
                position_amount = amount if side == 'buy' else -amount

            position_data = {
                'position_id': position_id,
                'user_id': user_id,
                'symbol': symbol,
                'side': side,
                'position_side': position_side,
                'amount': position_amount,
                'entry_price': price,
                'current_price': price,
                'unrealized_pnl': 0.0,
                'realized_pnl': 0.0,
                'margin_used': self._calculate_margin_used(symbol, position_amount, price),
                'created_at': datetime.now(timezone.utc).isoformat(),
                'updated_at': datetime.now(timezone.utc).isoformat(),
                'orders': [order_data['order_id']],
                'status': 'open'
            }

            success = self._save_position(position_data)
            if success:
                logger.info(f"✅ New position created: {position_id}")

            return success

        except Exception as e:
            logger.error(f"❌ Error creating new position: {e}")
            return False

    def _update_existing_position(self, position: Dict, side: str,
                                fill_amount: float, fill_price: float, order_data: Dict) -> bool:
        """Update existing position with new fill"""
        try:
            current_amount = position['amount']
            current_entry = position['entry_price']
            position_side = position['position_side']

            if (side == 'buy' and position_side == 'LONG') or (side == 'sell' and position_side == 'SHORT'):
                # Adding to position
                new_amount = current_amount + fill_amount

                # Calculate new weighted average entry price
                total_cost = (current_amount * current_entry) + (fill_amount * fill_price)
                new_entry_price = total_cost / new_amount if new_amount != 0 else current_entry

                logger.info(f"📈 Adding to position: {current_amount} + {fill_amount} = {new_amount}")

            else:
                # Reducing position (partial/full close)
                # Ensure we don't close more than we have
                actual_close_amount = min(fill_amount, current_amount)
                new_amount = current_amount - actual_close_amount
                new_entry_price = current_entry  # Entry price doesn't change when closing

                # Calculate realized P&L for the closed portion
                if position_side == 'LONG':
                    realized_pnl = (fill_price - current_entry) * actual_close_amount
                else:  # SHORT
                    realized_pnl = (current_entry - fill_price) * actual_close_amount

                # Update total realized P&L
                total_realized_pnl = position.get('realized_pnl', 0.0) + realized_pnl

                logger.info(f"📉 Reducing position: {current_amount} - {actual_close_amount} = {new_amount}")
                logger.info(f"💰 Realized P&L: ${realized_pnl:.2f}")

                if actual_close_amount < fill_amount:
                    logger.warning(f"⚠️ Attempted to close {fill_amount} but only {actual_close_amount} available")

                # Update position with realized P&L
                position['realized_pnl'] = total_realized_pnl

            # Update position data
            position['amount'] = new_amount
            position['entry_price'] = new_entry_price
            position['updated_at'] = datetime.now(timezone.utc).isoformat()

            # Add order ID to orders list if not already present
            if 'orders' not in position:
                position['orders'] = []

            current_order_id = order_data.get('order_id')
            if current_order_id and current_order_id not in position['orders']:
                position['orders'].append(current_order_id)

            # Close position if amount is zero or very small
            if abs(new_amount) < 0.000001:
                position['status'] = 'closed'
                position['amount'] = 0.0
                logger.info(f"🔒 Position closed: {position['symbol']} {position['position_side']}")

            # Update margin used
            position['margin_used'] = self._calculate_margin_used(
                position['symbol'], position['amount'], position['entry_price']
            )

            # Save updated position
            success = self._update_position_in_db(position)

            return success

        except Exception as e:
            logger.error(f"❌ Error updating existing position: {e}")
            return False

    def calculate_unrealized_pnl(self, position: Dict, current_price: float) -> float:
        """Calculate unrealized P&L for a position"""
        try:
            amount = position['amount']
            entry_price = position['entry_price']
            position_side = position['position_side']

            if amount == 0:
                return 0.0

            if position_side == 'LONG':
                unrealized_pnl = (current_price - entry_price) * amount
            else:  # SHORT
                unrealized_pnl = (entry_price - current_price) * amount

            return unrealized_pnl

        except Exception as e:
            logger.error(f"❌ Error calculating unrealized P&L: {e}")
            return 0.0

    def update_position_price(self, user_id: str, symbol: str, current_price: float) -> bool:
        """Update position with current market price and recalculate P&L"""
        try:
            positions = self.get_user_positions(user_id, symbol=symbol, status='open')

            for position in positions:
                # Calculate new unrealized P&L
                unrealized_pnl = self.calculate_unrealized_pnl(position, current_price)

                # Update position
                position['current_price'] = current_price
                position['unrealized_pnl'] = unrealized_pnl
                position['updated_at'] = datetime.now(timezone.utc).isoformat()

                # Save to database
                self._update_position_in_db(position)

                logger.debug(f"📊 Updated {symbol} price: ${current_price:.2f}, P&L: ${unrealized_pnl:.2f}")

            return True

        except Exception as e:
            logger.error(f"❌ Error updating position price: {e}")
            return False

    def get_position(self, user_id: str, symbol: str, position_side: str,
                    include_closed: bool = False) -> Optional[Dict]:
        """Get specific position"""
        try:
            with self.db.get_connection() as conn:
                if include_closed:
                    query = """
                        SELECT * FROM positions
                        WHERE user_id = ? AND symbol = ? AND position_side = ?
                        ORDER BY created_at DESC LIMIT 1
                    """
                else:
                    query = """
                        SELECT * FROM positions
                        WHERE user_id = ? AND symbol = ? AND position_side = ? AND status = 'open'
                        ORDER BY created_at DESC LIMIT 1
                    """

                row = conn.execute(query, (user_id, symbol, position_side)).fetchone()

                if row:
                    position = dict(row)
                    # Parse orders JSON
                    if position.get('orders'):
                        import json
                        try:
                            position['orders'] = json.loads(position['orders'])
                        except:
                            position['orders'] = []
                    else:
                        position['orders'] = []

                    return position

                return None

        except Exception as e:
            logger.error(f"❌ Error getting position: {e}")
            return None

    def get_user_positions(self, user_id: str, symbol: str = None,
                          status: str = None) -> List[Dict]:
        """Get all positions for a user"""
        try:
            query = "SELECT * FROM positions WHERE user_id = ?"
            params = [user_id]

            if symbol:
                query += " AND symbol = ?"
                params.append(symbol)

            if status:
                query += " AND status = ?"
                params.append(status)

            query += " ORDER BY updated_at DESC"

            with self.db.get_connection() as conn:
                rows = conn.execute(query, params).fetchall()

                positions = []
                for row in rows:
                    position = dict(row)
                    # Parse orders JSON
                    if position.get('orders'):
                        import json
                        try:
                            position['orders'] = json.loads(position['orders'])
                        except:
                            position['orders'] = []
                    else:
                        position['orders'] = []

                    positions.append(position)

                return positions

        except Exception as e:
            logger.error(f"❌ Error getting user positions: {e}")
            return []

    def _calculate_margin_used(self, symbol: str, amount: float, price: float) -> float:
        """Calculate margin used for position (simplified)"""
        try:
            # Simplified calculation - in reality this would depend on leverage
            # Assuming 10x leverage for futures
            leverage = 10.0
            notional_value = abs(amount) * price
            margin_used = notional_value / leverage

            return margin_used

        except Exception as e:
            logger.error(f"❌ Error calculating margin: {e}")
            return 0.0

    def _save_position(self, position_data: Dict) -> bool:
        """Save position to database"""
        try:
            import json

            with self.db.get_connection() as conn:
                conn.execute("""
                    INSERT INTO positions (
                        position_id, user_id, symbol, side, position_side, amount, entry_price,
                        current_price, unrealized_pnl, realized_pnl, margin_used,
                        created_at, updated_at, orders, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    position_data['position_id'],
                    position_data['user_id'],
                    position_data['symbol'],
                    position_data['side'],
                    position_data['position_side'],
                    position_data['amount'],
                    position_data['entry_price'],
                    position_data['current_price'],
                    position_data['unrealized_pnl'],
                    position_data['realized_pnl'],
                    position_data['margin_used'],
                    position_data['created_at'],
                    position_data['updated_at'],
                    json.dumps(position_data['orders']),
                    position_data['status']
                ))
                conn.commit()

            return True

        except Exception as e:
            logger.error(f"❌ Error saving position: {e}")
            return False

    def _update_position_in_db(self, position: Dict) -> bool:
        """Update position in database"""
        try:
            import json

            with self.db.get_connection() as conn:
                conn.execute("""
                    UPDATE positions SET
                        amount = ?, entry_price = ?, current_price = ?,
                        unrealized_pnl = ?, realized_pnl = ?, margin_used = ?,
                        updated_at = ?, orders = ?, status = ?
                    WHERE position_id = ?
                """, (
                    position['amount'],
                    position['entry_price'],
                    position['current_price'],
                    position['unrealized_pnl'],
                    position['realized_pnl'],
                    position['margin_used'],
                    position['updated_at'],
                    json.dumps(position['orders']),
                    position['status'],
                    position['position_id']
                ))
                conn.commit()

            return True

        except Exception as e:
            logger.error(f"❌ Error updating position in database: {e}")
            return False

# Global position manager instance
position_manager = PositionManager()
