"""
Discord Volatility Al<PERSON> - Sends price volatility alerts to Discord
"""

import logging
import discord
from typing import Optional
from datetime import datetime
from utils.config import get_admin_id, get_guild_id
from utils.ui_components import format_price_enhanced, format_percentage_enhanced
from utils.constants import EMOJI, DISCORD_FORMATTING, MESSAGE_TEMPLATES

logger = logging.getLogger(__name__)

class VolatilityAlertHandler:
    """Handler for sending volatility alerts to Discord"""

    def __init__(self, bot):
        self.bot = bot
        self.admin_id = get_admin_id()
        self.guild_id = get_guild_id()

    async def send_volatility_alert(self, symbol: str, timeframe: str, current_price: float,
                                  previous_price: float, percent_change: float, threshold: float):
        """Send volatility alert to Discord"""
        try:
            # Create alert embed
            embed = await self._create_alert_embed(
                symbol, timeframe, current_price, previous_price, percent_change, threshold
            )

            # Send to guild channel (không gửi DM)
            if self.guild_id:
                try:
                    guild = self.bot.get_guild(int(self.guild_id))
                    if guild:

                        # Tìm channel alerts với emoji: 🚨alerts hoặc alerts
                        target_channel = None
                        for channel in guild.channels:
                            if hasattr(channel, 'name') and isinstance(channel, discord.TextChannel):
                                if 'alerts' in channel.name.lower():
                                    target_channel = channel
                                    break

                        # Fallback to general
                        if not target_channel:
                            for channel in guild.channels:
                                if hasattr(channel, 'name') and isinstance(channel, discord.TextChannel):
                                    if 'general' in channel.name.lower():
                                        target_channel = channel
                                        break

                        if target_channel:
                            await target_channel.send(embed=embed)
                            logger.info(f"Sent volatility alert for {symbol} to #{target_channel.name}")
                        else:
                            logger.warning(f"No suitable channel found for volatility alert")
                except Exception as e:
                    logger.error(f"Failed to send alert to guild: {e}")

        except Exception as e:
            logger.error(f"Error sending volatility alert: {e}")

    async def _create_alert_embed(self, symbol: str, timeframe: str, current_price: float,
                                previous_price: float, percent_change: float, threshold: float) -> discord.Embed:
        """Create Discord embed for volatility alert with enhanced mobile-friendly formatting"""

        # Determine color and direction using constants
        is_increase = current_price > previous_price
        color = DISCORD_FORMATTING['colors']['profit'] if is_increase else DISCORD_FORMATTING['colors']['loss']
        direction = f"{EMOJI['up']} TĂNG" if is_increase else f"{EMOJI['down']} GIẢM"
        direction_emoji = EMOJI['green_circle'] if is_increase else EMOJI['red_circle']

        # Format timeframe
        timeframe_text = {
            '15m': '15 phút',
            '1h': '1 giờ',
            'daily': 'Hàng ngày'
        }.get(timeframe, timeframe)

        # Use template for consistent formatting
        title = MESSAGE_TEMPLATES['alert_volatility']['title_format'].format(
            alert=EMOJI['alarm']
        )

        description = MESSAGE_TEMPLATES['alert_volatility']['description_format'].format(
            symbol=symbol.replace('USDT', ''),
            timeframe=timeframe_text
        )

        # Create embed
        embed = discord.Embed(
            title=title,
            description=description,
            color=color,
            timestamp=discord.utils.utcnow()
        )

        # Add fields with enhanced formatting
        embed.add_field(
            name=f"{EMOJI['volume']} Thông tin biến động",
            value=f"{direction_emoji} **{direction}** {format_percentage_enhanced(abs(percent_change), show_sign=False, include_emoji=False)}\n"
                  f"{EMOJI['target']} Ngưỡng cảnh báo: {threshold}%",
            inline=False
        )

        embed.add_field(
            name=f"{EMOJI['price']} Giá cả",
            value=f"Trước: {format_price_enhanced(previous_price)}\n"
                  f"Hiện tại: {format_price_enhanced(current_price)}",
            inline=True
        )

        embed.add_field(
            name=f"{EMOJI['clock']} Khung thời gian",
            value=f"{timeframe_text}\n"
                  f"Thay đổi: {format_percentage_enhanced(percent_change)}",
            inline=True
        )

        # Add footer using constants
        embed.set_footer(text=DISCORD_FORMATTING['footers']['alerts'])

        # Add thumbnail
        embed.set_thumbnail(url="https://cdn-icons-png.flaticon.com/512/1828/1828640.png")

        return embed

# Global handler instance
_volatility_handler = None

def get_volatility_alert_handler(bot) -> VolatilityAlertHandler:
    """Get or create volatility alert handler"""
    global _volatility_handler
    if _volatility_handler is None:
        _volatility_handler = VolatilityAlertHandler(bot)
    return _volatility_handler
