"""
Discord Market Monitor <PERSON><PERSON> - Sends market monitor alerts to Discord
"""

import logging
import discord
from utils.config import get_admin_id, get_guild_id
from utils.ui_components import format_percentage_enhanced
from utils.constants import EMOJI, DISCORD_FORMATTING

logger = logging.getLogger(__name__)

class MarketMonitorAlertHandler:
    """Handler for sending market monitor alerts to Discord"""

    def __init__(self, bot):
        self.bot = bot
        self.admin_id = get_admin_id()
        self.guild_id = get_guild_id()

    async def send_market_alert(self, alert_type: str, alert_subtype: str,
                               current_rate: float, previous_rate: float, threshold: float):
        """Send market monitor alert to Discord"""
        try:
            # Create alert embed based on type
            if alert_type == "earn_rate":
                embed = await self._create_earn_rate_embed(
                    alert_subtype, current_rate, previous_rate, threshold
                )
            elif alert_type == "p2p_rate":
                embed = await self._create_p2p_rate_embed(
                    current_rate, previous_rate, threshold
                )
            else:
                logger.warning(f"Unknown alert type: {alert_type}")
                return

            # Send to guild channels (không gửi DM)
            if self.guild_id:
                try:
                    guild = self.bot.get_guild(int(self.guild_id))
                    if guild:

                        # Tìm channel alerts với emoji: 🚨alerts hoặc alerts
                        target_channel = None
                        for channel in guild.channels:
                            if hasattr(channel, 'name') and isinstance(channel, discord.TextChannel):
                                if 'alerts' in channel.name.lower():
                                    target_channel = channel
                                    break

                        # Fallback to general
                        if not target_channel:
                            for channel in guild.channels:
                                if hasattr(channel, 'name') and isinstance(channel, discord.TextChannel):
                                    if 'general' in channel.name.lower():
                                        target_channel = channel
                                        break

                        if target_channel:
                            await target_channel.send(embed=embed)
                            logger.info(f"Sent {alert_type} alert to #{target_channel.name}")
                        else:
                            logger.warning(f"No suitable channel found for {alert_type} alert")
                except Exception as e:
                    logger.error(f"Failed to send alert to guild: {e}")

        except Exception as e:
            logger.error(f"Error sending market monitor alert: {e}")

    async def _create_earn_rate_embed(self, alert_subtype: str, current_rate: float,
                                     previous_rate: float, threshold: float) -> discord.Embed:
        """Create embed for earn rate alerts with enhanced formatting"""

        # Determine color and message based on alert type using constants
        if alert_subtype == "threshold_reached":
            color = DISCORD_FORMATTING['colors']['success']
            title = f"{EMOJI['target']} NGƯỠNG LÃI SUẤT ĐẠT ĐƯỢC"
            description = f"**USDT Flexible Earn** đã đạt ngưỡng **{threshold}%** APY!"
        else:  # rate_increase
            color = DISCORD_FORMATTING['colors']['warning']
            title = f"{EMOJI['chart']} LÃI SUẤT TĂNG MẠNH"
            increase = ((current_rate - previous_rate) / previous_rate) * 100
            description = f"**USDT Flexible Earn** tăng **{increase:.1f}%** trong thời gian ngắn!"

        embed = discord.Embed(
            title=title,
            description=description,
            color=color,
            timestamp=discord.utils.utcnow()
        )

        # Add rate information with enhanced formatting
        embed.add_field(
            name=f"{EMOJI['price']} Thông tin lãi suất",
            value=f"Trước: **{previous_rate:.2f}%** APY\n"
                  f"Hiện tại: **{current_rate:.2f}%** APY\n"
                  f"Thay đổi: **{format_percentage_enhanced(current_rate - previous_rate, include_emoji=False)}**",
            inline=True
        )

        # Add threshold info
        embed.add_field(
            name=f"{EMOJI['target']} Ngưỡng cảnh báo",
            value=f"**{threshold}%** APY",
            inline=True
        )

        # Add recommendation
        embed.add_field(
            name=f"{EMOJI['bulb']} Khuyến nghị",
            value="Xem xét đầu tư USDT Flexible Earn\ntrên Binance để tận dụng lãi suất cao!",
            inline=False
        )

        embed.set_footer(text=DISCORD_FORMATTING['footers']['alerts'])
        embed.set_thumbnail(url="https://cdn-icons-png.flaticon.com/512/2721/2721279.png")

        return embed

    async def _create_p2p_rate_embed(self, current_rate: float,
                                    previous_rate: float, threshold: float) -> discord.Embed:
        """Create embed for P2P rate alerts - only buy rate (buyer demand) alerts"""

        increase = ((current_rate - previous_rate) / previous_rate) * 100

        embed = discord.Embed(
            title=f"{EMOJI['alarm']} GIÁ MUA P2P USDT/VND TĂNG MẠNH",
            description=f"Giá **mua** USDT trên Binance P2P tăng **{increase:.1f}%**!\n"
                       f"**Nhu cầu mua USDT tăng cao** - Dấu hiệu thị trường tích cực",
            color=DISCORD_FORMATTING['colors']['warning'],
            timestamp=discord.utils.utcnow()
        )

        # Add price information with enhanced formatting
        embed.add_field(
            name=f"{EMOJI['trade']} Thông tin giá mua",
            value=f"Trước: **{previous_rate:,.0f}** VND\n"
                  f"Hiện tại: **{current_rate:,.0f}** VND\n"
                  f"Thay đổi: **+{current_rate - previous_rate:,.0f}** VND",
            inline=True
        )

        # Add percentage change
        embed.add_field(
            name=f"{EMOJI['volume']} Tỷ lệ thay đổi",
            value=f"**{format_percentage_enhanced(increase, include_emoji=False)}**\n"
                  f"Ngưỡng: **{threshold}%**",
            inline=True
        )

        # Add market impact analysis focused on buy rate significance
        embed.add_field(
            name=f"{EMOJI['magnifying_glass']} Phân tích thị trường",
            value=f"{EMOJI['chart']} **Nhu cầu mua USDT tăng mạnh**\n"
                  f"{EMOJI['price']} Người dùng sẵn sàng trả giá cao hơn\n"
                  f"{EMOJI['rocket']} Có thể báo hiệu xu hướng tăng giá\n"
                  f"{EMOJI['lightning']} Thanh khoản thị trường đang tích cực",
            inline=False
        )

        embed.set_footer(text=DISCORD_FORMATTING['footers']['alerts'])
        embed.set_thumbnail(url="https://cdn-icons-png.flaticon.com/512/2830/2830284.png")

        return embed

# Global handler instance
_market_monitor_handler = None

def get_market_monitor_alert_handler(bot) -> MarketMonitorAlertHandler:
    """Get or create market monitor alert handler"""
    global _market_monitor_handler
    if _market_monitor_handler is None:
        _market_monitor_handler = MarketMonitorAlertHandler(bot)
    return _market_monitor_handler
