import logging
import discord
from typing import Optional, Dict, Any
from datetime import datetime, timezone

from services.market.news_service import get_news_service
from services.core.error_service import handle_service_errors

logger = logging.getLogger(__name__)

class MarketNewsHandler:
    def __init__(self, bot):
        self.bot = bot
        self.news_service = get_news_service()

        self.news_channel_name = "market-news"
        self.embed_color = 0x00ff88
        self.error_color = 0xff4444

        logger.info("Market News Handler initialized")

    @handle_service_errors
    async def send_daily_market_report(self) -> bool:
        try:
            logger.info("Starting daily market report generation...")

            report = await self.news_service.generate_daily_report()

            if not report.get("success", False):
                logger.error(f"Failed to generate report: {report.get('error', 'Unknown error')}")
                await self._send_error_notification("Failed to generate daily market report")
                return False

            embed = self._create_market_report_embed(report)

            channel = await self._get_or_create_news_channel()
            if not channel:
                logger.error("Could not find or create news channel")
                return False

            await channel.send(embed=embed)
            logger.info(f"Daily market report sent to #{channel.name}")

            return True

        except Exception as e:
            logger.error(f"Error sending daily market report: {e}")
            await self._send_error_notification(f"Error sending daily report: {str(e)}")
            return False

    def _create_market_report_embed(self, report: Dict[str, Any]) -> discord.Embed:
        """Create Discord embed for market report"""
        try:
            sections = report.get("sections", {})
            timestamp = report.get("timestamp", datetime.now(timezone.utc))

            # Create main embed
            embed = discord.Embed(
                title=report.get("title", "📰 DAILY MARKET REPORT 🌍"),
                color=self.embed_color,
                timestamp=timestamp
            )

            # Add crypto overview
            crypto_overview = sections.get("crypto_overview", "")
            if crypto_overview:
                embed.description = crypto_overview

            # Add global markets as field
            global_markets = sections.get("global_markets", "")
            if global_markets:
                embed.add_field(
                    name="🌍 Global Markets",
                    value=global_markets,
                    inline=False
                )

            # Add trending coins
            trending_coins = sections.get("trending_coins", "")
            if trending_coins:
                embed.add_field(
                    name="🔥 Trending Coins",
                    value=trending_coins,
                    inline=True
                )

            # Add market sentiment
            market_sentiment = sections.get("market_sentiment", "")
            if market_sentiment:
                embed.add_field(
                    name="📊 Market Sentiment",
                    value=market_sentiment,
                    inline=True
                )

            # Add crypto news
            crypto_news = sections.get("crypto_news", "")
            if crypto_news:
                # Split into title and content
                lines = crypto_news.split('\n', 1)
                title = lines[0] if lines else "🔥 CRYPTO NEWS"
                content = lines[1] if len(lines) > 1 else "No news available"

                # Truncate if too long
                if len(content) > 1024:
                    content = content[:1021] + "..."

                embed.add_field(
                    name=title,
                    value=content,
                    inline=False
                )

            # Add financial news
            financial_news = sections.get("financial_news", "")
            if financial_news:
                # Split into title and content
                lines = financial_news.split('\n', 1)
                title = lines[0] if lines else "💼 FINANCIAL NEWS"
                content = lines[1] if len(lines) > 1 else "No news available"

                # Truncate if too long
                if len(content) > 1024:
                    content = content[:1021] + "..."

                embed.add_field(
                    name=title,
                    value=content,
                    inline=False
                )

            # Add footer
            embed.set_footer(
                text="ChartFix Market News • Cập nhật hàng ngày lúc 00:00 UTC",
                icon_url="https://cryptologos.cc/logos/bitcoin-btc-logo.png"
            )

            return embed

        except Exception as e:
            logger.error(f"Error creating market report embed: {e}")
            # Return error embed
            return discord.Embed(
                title="❌ Error Creating Report",
                description=f"Failed to create market report: {str(e)}",
                color=self.error_color,
                timestamp=datetime.now(timezone.utc)
            )

    async def _get_or_create_news_channel(self) -> Optional[discord.TextChannel]:
        """Get or create the market news channel"""
        try:
            # Try to find existing channel in all guilds
            for guild in self.bot.guilds:
                for channel in guild.text_channels:
                    if channel.name == self.news_channel_name:
                        logger.info(f"Found existing news channel: #{channel.name} in {guild.name}")
                        return channel

            # If not found, create in the first available guild
            if self.bot.guilds:
                guild = self.bot.guilds[0]

                # Check if bot has permission to create channels
                if guild.me.guild_permissions.manage_channels:
                    channel = await guild.create_text_channel(
                        name=self.news_channel_name,
                        topic="📰 Automated daily market news and reports",
                        reason="Created by ChartFix bot for market news"
                    )
                    logger.info(f"Created new news channel: #{channel.name} in {guild.name}")

                    # Send welcome message
                    welcome_embed = discord.Embed(
                        title="📰 Welcome to Market News!",
                        description="This channel will receive automated daily market reports at 00:00 UTC.\n\n"
                                  "**What you'll get:**\n"
                                  "📊 Crypto market overview\n"
                                  "🌍 Global market indices\n"
                                  "🔥 Trending cryptocurrencies\n"
                                  "📰 Latest crypto & financial news\n"
                                  "🧠 Market sentiment analysis",
                        color=self.embed_color,
                        timestamp=datetime.now(timezone.utc)
                    )
                    welcome_embed.set_footer(text="ChartFix Market News System")
                    await channel.send(embed=welcome_embed)

                    return channel
                else:
                    logger.error(f"Bot doesn't have permission to create channels in {guild.name}")
                    # Try to use any available channel as fallback
                    return await self._find_fallback_channel(guild)
            else:
                logger.error("Bot is not in any guilds")
                return None

        except Exception as e:
            logger.error(f"Error getting/creating news channel: {e}")
            return None

    async def _find_fallback_channel(self, guild: discord.Guild) -> Optional[discord.TextChannel]:
        """Find a fallback channel when bot can't create channels"""
        try:
            # Look for general channels where bot can send messages
            fallback_names = ['general', 'bot-commands', 'crypto', 'trading', 'market']

            for channel in guild.text_channels:
                if any(name in channel.name.lower() for name in fallback_names):
                    if channel.permissions_for(guild.me).send_messages:
                        logger.info(f"Using fallback channel: #{channel.name} for market news")

                        # Send setup message
                        setup_embed = discord.Embed(
                            title="⚠️ Market News Setup Required",
                            description=f"**Bot needs a dedicated channel for market news!**\n\n"
                                      f"Please create a channel named `{self.news_channel_name}` or give the bot "
                                      f"`Manage Channels` permission to auto-create it.\n\n"
                                      f"For now, market reports will be sent here.",
                            color=0xffa500,  # Orange warning color
                            timestamp=datetime.now(timezone.utc)
                        )
                        setup_embed.set_footer(text="ChartFix Market News System")
                        await channel.send(embed=setup_embed)

                        return channel

            # If no suitable channel found, use the first available text channel
            for channel in guild.text_channels:
                if channel.permissions_for(guild.me).send_messages:
                    logger.warning(f"Using first available channel: #{channel.name} for market news")
                    return channel

            logger.error("No suitable channel found for market news")
            return None

        except Exception as e:
            logger.error(f"Error finding fallback channel: {e}")
            return None

    async def _send_error_notification(self, error_message: str):
        """Send error notification to admin or log channel"""
        try:
            # Try to send to news channel first
            channel = await self._get_or_create_news_channel()
            if channel:
                error_embed = discord.Embed(
                    title="❌ Market News Error",
                    description=error_message,
                    color=self.error_color,
                    timestamp=datetime.now(timezone.utc)
                )
                error_embed.set_footer(text="ChartFix Market News System")
                await channel.send(embed=error_embed)
            else:
                logger.error(f"Could not send error notification: {error_message}")

        except Exception as e:
            logger.error(f"Error sending error notification: {e}")

    async def test_market_report(self) -> bool:
        """Test function to manually trigger market report"""
        logger.info("Testing market report generation...")
        return await self.send_daily_market_report()

# Singleton instance
_market_news_handler_instance = None

def get_market_news_handler(bot) -> MarketNewsHandler:
    """Get singleton instance of MarketNewsHandler"""
    global _market_news_handler_instance
    if _market_news_handler_instance is None:
        _market_news_handler_instance = MarketNewsHandler(bot)
    return _market_news_handler_instance
