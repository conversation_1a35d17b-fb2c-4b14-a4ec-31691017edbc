import logging
import discord
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional

from utils.config import get_guild_id
from utils.ui_components import format_price_enhanced, format_percentage_enhanced
from utils.constants import EMOJI, DISCORD_FORMATTING

logger = logging.getLogger(__name__)

class TradingTimeAlertHandler:
    def __init__(self, bot):
        self.bot = bot
        self.guild_id = get_guild_id()
        # Hardcoded channel name per user preference (no config files)
        self.channel_name = "time-trade"

        # Phase 2: Persistent Messages System
        self.persistent_messages = {
            'market_session_status': None,  # Message ID for market session status
            'market_overview': None         # Message ID for market overview
        }

    async def get_or_create_time_trade_channel(self, guild) -> Optional[discord.TextChannel]:
        try:
            existing_channel = discord.utils.get(guild.channels, name=self.channel_name)
            if existing_channel:
                return existing_channel

            if guild.me.guild_permissions.manage_channels:
                channel = await guild.create_text_channel(
                    self.channel_name,
                    topic="🕐 Optimal Cryptocurrency Trading Time Analysis & Alerts",
                    reason="ChartFix Trading Time Analysis Channel"
                )

                welcome_embed = discord.Embed(
                    title="🕐 Trading Time Analysis Channel",
                    description="**Chào mừng đến với kênh phân tích thời gian giao dịch tối ưu!**\n\n"
                               "📊 **Chức năng chính:**\n"
                               "• Phân tích khung thời gian thanh khoản cao\n"
                               "• Cảnh báo tăng đột biến khối lượng\n"
                               "• Cơ hội giao dịch dựa trên tin tức\n"
                               "• Giám sát biến động thị trường real-time\n\n"
                               "🕐 **Khung thời gian tối ưu:**\n"
                               "• **Asian Peak**: 07:00-11:00\n"
                               "• **European Surge**: 15:00-19:00\n"
                               "• **US Peak**: 20:00-04:00",
                    color=0x00ff88,
                    timestamp=discord.utils.utcnow()
                )
                welcome_embed.set_footer(text="ChartFix Trading Time Analysis System")
                await channel.send(embed=welcome_embed)

                logger.info(f"Created new time-trade channel: #{channel.name} in {guild.name}")
                return channel
            else:
                logger.warning(f"Bot lacks permission to create channels in {guild.name}")
                return None

        except Exception as e:
            logger.error(f"Error getting/creating time-trade channel: {e}")
            return None

    async def _update_persistent_message(self, message_type: str, embed: discord.Embed, channel: discord.TextChannel):
        """
        Update or create persistent messages for status updates with auto-pin functionality.
        Phase 2: Hybrid messaging strategy with auto-pin implementation.
        """
        try:
            message_id = self.persistent_messages.get(message_type)

            if message_id:
                try:
                    # Try to edit existing message
                    message = await channel.fetch_message(message_id)
                    await message.edit(embed=embed)

                    # Auto-pin if not already pinned
                    if not message.pinned:
                        await self._auto_pin_message(message, channel, message_type)

                    logger.info(f"Updated persistent {message_type} message")
                    return
                except discord.NotFound:
                    # Message was deleted, create new one
                    logger.warning(f"Persistent {message_type} message not found, creating new one")
                    self.persistent_messages[message_type] = None
                except discord.HTTPException as e:
                    logger.error(f"Failed to edit persistent {message_type} message: {e}")
                    return

            # Create new persistent message
            message = await channel.send(embed=embed)
            self.persistent_messages[message_type] = message.id

            # Auto-pin new persistent message
            await self._auto_pin_message(message, channel, message_type)

            logger.info(f"Created and pinned new persistent {message_type} message (ID: {message.id})")

        except Exception as e:
            logger.error(f"Error updating persistent {message_type} message: {e}")

    async def _auto_pin_message(self, message: discord.Message, channel: discord.TextChannel, message_type: str):
        """
        Auto-pin persistent status messages with permission checks and error handling.
        """
        try:
            # Check bot permissions
            if not channel.permissions_for(channel.guild.me).manage_messages:
                logger.warning(f"Bot lacks permission to pin messages in {channel.name}")
                return

            # Pin the message
            await message.pin()
            logger.info(f"Auto-pinned {message_type} message in {channel.name}")

        except discord.HTTPException as e:
            if "already pinned" in str(e).lower():
                logger.debug(f"Message {message_type} already pinned in {channel.name}")
            elif "maximum number of pins" in str(e).lower():
                logger.warning(f"Channel {channel.name} has reached maximum pins, cannot pin {message_type}")
                # Optionally unpin oldest pins to make room
                await self._manage_pin_limit(channel, message, message_type)
            else:
                logger.error(f"Failed to pin {message_type} message: {e}")
        except Exception as e:
            logger.error(f"Error auto-pinning {message_type} message: {e}")

    async def _manage_pin_limit(self, channel: discord.TextChannel, new_message: discord.Message, message_type: str):
        """
        Manage pin limit by unpinning old non-persistent messages to make room for important status updates.
        """
        try:
            pinned_messages = await channel.pins()

            # Find non-persistent pinned messages (not our status messages)
            persistent_message_ids = set(self.persistent_messages.values())
            non_persistent_pins = [msg for msg in pinned_messages if msg.id not in persistent_message_ids]

            if non_persistent_pins:
                # Unpin oldest non-persistent message
                oldest_pin = min(non_persistent_pins, key=lambda m: m.created_at)
                await oldest_pin.unpin()
                logger.info(f"Unpinned old message to make room for {message_type}")

                # Now try to pin our message
                await new_message.pin()
                logger.info(f"Successfully pinned {message_type} after managing pin limit")
            else:
                logger.warning(f"All pinned messages are persistent, cannot unpin to make room for {message_type}")

        except Exception as e:
            logger.error(f"Error managing pin limit: {e}")

    async def send_trading_time_alert(self, alert_type: str, alert_data: Dict[str, Any]):
        try:
            for guild in self.bot.guilds:
                channel = await self.get_or_create_time_trade_channel(guild)
                if channel:
                    # Phase 2: Hybrid messaging strategy
                    if alert_type == "volume_surge":
                        # Event message - always create new
                        embed = await self._create_volume_surge_embed(alert_data)
                        await channel.send(embed=embed)
                        logger.info(f"Sent volume surge event to {guild.name}")

                    elif alert_type == "price_movement":
                        # Event message - always create new
                        embed = await self._create_price_movement_embed(alert_data)
                        await channel.send(embed=embed)
                        logger.info(f"Sent price movement event to {guild.name}")

                    elif alert_type == "news_opportunity":
                        # Event message - always create new
                        embed = await self._create_news_opportunity_embed(alert_data)
                        await channel.send(embed=embed)
                        logger.info(f"Sent news opportunity event to {guild.name}")

                    elif alert_type == "market_session":
                        # Persistent message - update existing
                        embed = await self._create_market_session_embed(alert_data)
                        await self._update_persistent_message('market_session_status', embed, channel)
                        logger.info(f"Updated market session status for {guild.name}")

                    elif alert_type == "optimal_time_analysis":
                        # Persistent message - update existing
                        embed = await self._create_optimal_time_embed(alert_data)
                        await self._update_persistent_message('market_overview', embed, channel)
                        logger.info(f"Updated market overview for {guild.name}")

                    else:
                        logger.warning(f"Unknown alert type: {alert_type}")
                        continue

        except Exception as e:
            logger.error(f"Error sending trading time alert: {e}")

    async def _create_volume_surge_embed(self, alert_data: Dict[str, Any]) -> discord.Embed:
        surges = alert_data.get('surges', [])

        if not surges:
            return discord.Embed(title="No volume surges detected", color=0x3498db)

        # Phase 3: Smart Grouping Logic
        if len(surges) >= 3:
            return await self._create_grouped_volume_embed(surges)
        else:
            return await self._create_single_volume_embed(surges[0])

    async def _create_single_volume_embed(self, surge: Dict[str, Any]) -> discord.Embed:
        """Create simplified embed for single volume surge alert."""
        embed = discord.Embed(
            title=f"🚀 Volume Surge Alert",
            description=f"**{surge['symbol']} experiencing significant volume increase**",
            color=0xff6b35,
            timestamp=discord.utils.utcnow()
        )

        symbol = surge['symbol']
        volume_increase = surge['volume_increase_pct']
        price_change = surge['price_change_24h']
        threshold = surge['threshold']
        rank = surge.get('market_cap_rank', 'N/A')

        direction_emoji = "🟢" if price_change > 0 else "🔴" if price_change < 0 else "⚪"

        surge_text = f"{direction_emoji} **{symbol}** (#{rank})\n"
        surge_text += f"📈 Volume: +{volume_increase:.1f}% (>{threshold}%)\n"
        surge_text += f"💰 Price 24h: {price_change:+.2f}%"

        embed.add_field(
            name="🔥 Volume Surge Details",
            value=surge_text,
            inline=False
        )

        embed.set_footer(text="ChartFix Trading Time Analysis")
        embed.set_thumbnail(url="https://cdn-icons-png.flaticon.com/512/2942/2942813.png")

        return embed

    async def _create_grouped_volume_embed(self, surges: list) -> discord.Embed:
        """Create embed for multiple volume surge alerts (Phase 3: Smart Grouping)."""
        embed = discord.Embed(
            title=f"🚀 Multiple Volume Surges Detected",
            description=f"**{len(surges)} coins experiencing significant volume increases**",
            color=0xff6b35,
            timestamp=discord.utils.utcnow()
        )

        surge_text = ""
        for i, surge in enumerate(surges[:5]):  # Limit to top 5 to avoid message length issues
            symbol = surge['symbol']
            volume_increase = surge['volume_increase_pct']
            price_change = surge['price_change_24h']
            threshold = surge['threshold']
            rank = surge.get('market_cap_rank', 'N/A')

            direction_emoji = "🟢" if price_change > 0 else "🔴" if price_change < 0 else "⚪"

            surge_text += f"{direction_emoji} **{symbol}** (#{rank})\n"
            surge_text += f"📈 Volume: +{volume_increase:.1f}% (>{threshold}%)\n"
            surge_text += f"💰 Price: {price_change:+.2f}%\n\n"

        if len(surges) > 5:
            surge_text += f"*... and {len(surges) - 5} more coins*"

        embed.add_field(
            name="🔥 Top Volume Surges",
            value=surge_text or "No data available",
            inline=False
        )

        embed.set_footer(text="ChartFix Trading Time Analysis")
        embed.set_thumbnail(url="https://cdn-icons-png.flaticon.com/512/2942/2942813.png")

        return embed

    async def _create_price_movement_embed(self, alert_data: Dict[str, Any]) -> discord.Embed:
        movements = alert_data.get('movements', [])

        if not movements:
            return discord.Embed(title="No price movements detected", color=0x3498db)

        # Group by direction for better display
        if len(movements) >= 3:
            return await self._create_grouped_price_embed(movements)
        else:
            return await self._create_single_price_embed(movements[0])

    async def _create_single_price_embed(self, movement: Dict[str, Any]) -> discord.Embed:
        """Create simplified embed for single price movement alert."""
        symbol = movement['symbol']
        price_change = movement['price_change_24h']
        threshold = movement['threshold']
        direction = movement['direction']
        rank = movement.get('market_cap_rank', 'N/A')

        direction_emoji = "🟢" if direction == 'up' else "🔴"
        direction_text = "TĂNG" if direction == 'up' else "GIẢM"
        color = 0x00ff88 if direction == 'up' else 0xff4444

        embed = discord.Embed(
            title=f"📈 Price Movement Alert",
            description=f"**{symbol} experiencing significant price {direction_text.lower()}**",
            color=color,
            timestamp=discord.utils.utcnow()
        )

        movement_text = f"{direction_emoji} **{symbol}** (#{rank})\n"
        movement_text += f"📊 Price 24h: {price_change:+.2f}% (>{threshold}%)\n"
        movement_text += f"🎯 Direction: {direction_text}"

        embed.add_field(
            name="🔥 Price Movement Details",
            value=movement_text,
            inline=False
        )

        embed.set_footer(text="ChartFix Trading Time Analysis")
        embed.set_thumbnail(url="https://cdn-icons-png.flaticon.com/512/2942/2942813.png")

        return embed

    async def _create_grouped_price_embed(self, movements: list) -> discord.Embed:
        """Create embed for multiple price movement alerts."""
        up_movements = [m for m in movements if m['direction'] == 'up']
        down_movements = [m for m in movements if m['direction'] == 'down']

        embed = discord.Embed(
            title=f"📈 Multiple Price Movements Detected",
            description=f"**{len(movements)} coins experiencing significant price changes**",
            color=0xffa500,
            timestamp=discord.utils.utcnow()
        )

        movement_text = ""
        for movement in movements[:5]:  # Limit to top 5
            symbol = movement['symbol']
            price_change = movement['price_change_24h']
            threshold = movement['threshold']
            rank = movement.get('market_cap_rank', 'N/A')
            direction_emoji = "🟢" if movement['direction'] == 'up' else "🔴"

            movement_text += f"{direction_emoji} **{symbol}** (#{rank})\n"
            movement_text += f"📊 Price 24h: {price_change:+.2f}% (>{threshold}%)\n\n"

        if len(movements) > 5:
            movement_text += f"*... and {len(movements) - 5} more coins*"

        embed.add_field(
            name="🔥 Top Price Movements",
            value=movement_text or "No data available",
            inline=False
        )

        embed.set_footer(text="ChartFix Trading Time Analysis")
        embed.set_thumbnail(url="https://cdn-icons-png.flaticon.com/512/2942/2942813.png")

        return embed

    async def _create_news_opportunity_embed(self, alert_data: Dict[str, Any]) -> discord.Embed:
        opportunities = alert_data.get('opportunities', [])

        embed = discord.Embed(
            title="📰 News Trading Opportunities",
            description=f"**{len(opportunities)} upcoming high-impact event(s)**",
            color=0x3498db,
            timestamp=discord.utils.utcnow()
        )

        if opportunities:
            events_text = ""
            for opp in opportunities[:3]:
                currency = opp['currency']
                title = opp['title']
                time_until = opp['time_until_minutes']
                impact = opp['impact']
                recommendation = opp['trading_recommendation']

                impact_emoji = "🔴" if impact.lower() == "high" else "🟡" if impact.lower() == "medium" else "🟢"

                events_text += f"{impact_emoji} **{currency}** - {title}\n"
                events_text += f"⏰ In {time_until} minutes\n"
                events_text += f"💡 {recommendation}\n\n"

            embed.add_field(
                name="📅 Upcoming Events",
                value=events_text,
                inline=False
            )
        else:
            embed.add_field(
                name="📅 No Immediate Opportunities",
                value="No high-impact events in the next hour",
                inline=False
            )

        embed.set_footer(text="ChartFix Economic Calendar Integration")
        embed.set_thumbnail(url="https://cdn-icons-png.flaticon.com/512/3281/3281289.png")

        return embed

    async def _create_market_session_embed(self, alert_data: Dict[str, Any]) -> discord.Embed:
        session_info = alert_data.get('session_info', {})
        active_sessions = session_info.get('active_sessions', [])
        next_session = session_info.get('next_session')
        is_optimal = session_info.get('is_optimal_trading_time', False)

        color = 0x00ff88 if is_optimal else 0xffa500
        title = "🟢 Optimal Trading Time" if is_optimal else "🟡 Standard Trading Time"

        embed = discord.Embed(
            title=title,
            description="Current market session analysis",
            color=color,
            timestamp=discord.utils.utcnow()
        )

        if active_sessions:
            session_text = ""
            for session in active_sessions:
                session_text += f"🕐 **{session['description']}**\n"
                # Convert UTC to VN time (+7) and remove UTC
                start_hour = (int(session['start'].split(':')[0]) + 7) % 24
                start_min = session['start'].split(':')[1]
                end_hour = (int(session['end'].split(':')[0]) + 7) % 24
                end_min = session['end'].split(':')[1]
                session_text += f"⏰ {start_hour:02d}:{start_min} - {end_hour:02d}:{end_min}\n\n"

            embed.add_field(
                name="🔥 Active High-Volume Sessions",
                value=session_text,
                inline=False
            )
        else:
            embed.add_field(
                name="📊 Current Status",
                value="⏸️ Outside peak trading hours\n"
                      "💡 Lower liquidity expected",
                inline=False
            )

        if next_session:
            embed.add_field(
                name="⏭️ Next Optimal Session",
                value=f"🕐 **{next_session['description']}**\n"
                      f"⏰ Starts at {(int(next_session['start'].split(':')[0]) + 7) % 24:02d}:{next_session['start'].split(':')[1]}\n"
                      f"🕒 In {next_session['time_until']:.1f} hours",
                inline=False
            )

        embed.set_footer(text="ChartFix Market Session Analysis")
        embed.set_thumbnail(url="https://cdn-icons-png.flaticon.com/512/2942/2942910.png")

        return embed

    async def _create_optimal_time_embed(self, alert_data: Dict[str, Any]) -> discord.Embed:
        volume_analysis = alert_data.get('volume_analysis', {})
        session_info = alert_data.get('session_info', {})

        activity_level = volume_analysis.get('market_activity_level', 'unknown')
        total_volume = volume_analysis.get('total_market_volume', 0)
        high_volume_coins = volume_analysis.get('high_volume_coins', [])

        activity_colors = {
            'high': 0x00ff88,
            'normal': 0xffa500,
            'low': 0xff4444,
            'unknown': 0x95a5a6
        }

        embed = discord.Embed(
            title="📊 Optimal Trading Time Analysis",
            description=f"**Market Activity Level: {activity_level.upper()}**",
            color=activity_colors.get(activity_level, 0x95a5a6),
            timestamp=discord.utils.utcnow()
        )

        embed.add_field(
            name="📈 Market Overview",
            value=f"💰 Total Volume: ${total_volume:,.0f}\n"
                  f"🔥 High Activity Coins: {len(high_volume_coins)}\n"
                  f"⚡ Activity Level: {activity_level.title()}",
            inline=True
        )

        if high_volume_coins:
            top_coins_text = ""
            for coin in high_volume_coins[:3]:
                symbol = coin['symbol']
                ratio = coin['volume_to_mcap_ratio']
                price_change = coin['price_change_24h']

                direction = "🟢" if price_change > 0 else "🔴"
                top_coins_text += f"{direction} **{symbol}**: {ratio:.1f}% ratio\n"

            embed.add_field(
                name="🚀 Most Active Coins",
                value=top_coins_text,
                inline=True
            )

        is_optimal = session_info.get('is_optimal_trading_time', False)
        recommendation = "🎯 **Optimal time for trading**" if is_optimal else "⏸️ **Consider waiting for peak hours**"

        embed.add_field(
            name="💡 Trading Recommendation",
            value=recommendation,
            inline=False
        )

        embed.set_footer(text="ChartFix Comprehensive Market Analysis")
        embed.set_thumbnail(url="https://cdn-icons-png.flaticon.com/512/3281/3281307.png")

        return embed

def get_trading_time_alert_handler(bot) -> TradingTimeAlertHandler:
    return TradingTimeAlertHandler(bot)
