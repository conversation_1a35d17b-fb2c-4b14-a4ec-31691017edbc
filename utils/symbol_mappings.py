import logging
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

COINGECKO_SYMBOL_MAPPINGS: Dict[str, str] = {
    'btc': 'bitcoin',
    'eth': 'ethereum',
    'bnb': 'binancecoin',
    'ada': 'cardano',
    'sol': 'solana',
    'dot': 'polkadot',
    'link': 'chainlink',
    'matic': 'matic-network',
    'avax': 'avalanche-2',
    'atom': 'cosmos',
    'near': 'near',
    'ftm': 'fantom',
    'algo': 'algorand',
    'xlm': 'stellar',
    'vet': 'vechain',
    'icp': 'internet-computer',
    'fil': 'filecoin',
    'trx': 'tron',
    'etc': 'ethereum-classic',
    'xmr': 'monero',
    'bch': 'bitcoin-cash',
    'ltc': 'litecoin',
    'uni': 'uniswap',
    'doge': 'dogecoin',
    'shib': 'shiba-inu',
    'ena': 'ethena',
    'pepe': 'pepe',
    'wif': 'dogwifcoin',
    'bonk': 'bonk',
    'jup': 'jupiter-exchange-solana',
    'pyth': 'pyth-network',
    'jto': 'jito-governance-token',
    'sei': 'sei-network',
    'tia': 'celestia',
    'strk': 'starknet',
    'manta': 'manta-network',
    'alt': 'altlayer',
    'wen': 'wen-4',
    'bome': 'book-of-meme',
    'slerf': 'slerf',
    'w': 'wormhole',
    'tnsr': 'tensor',
    'saga': 'saga-2',
    'omni': 'omni-network',
    'rez': 'renzo',
    'io': 'io',
    'zkj': 'zkjump',
    'zro': 'layerzero',
    'g': 'gravity',
    'hmstr': 'hamster-kombat',
    'cati': 'catizen',
    'eigen': 'eigenlayer'
}

def get_coingecko_id(symbol: str) -> str:
    if not symbol or not isinstance(symbol, str):
        return ""

    base_symbol = symbol.upper().replace('USDT', '').replace('USDC', '').replace('BUSD', '')
    base_symbol = base_symbol.lower()

    coin_id = COINGECKO_SYMBOL_MAPPINGS.get(base_symbol, base_symbol)

    logger.debug(f"Symbol mapping: {symbol} -> {base_symbol} -> {coin_id}")
    return coin_id

def is_symbol_supported(symbol: str) -> bool:
    if not symbol or not isinstance(symbol, str):
        return False

    base_symbol = symbol.upper().replace('USDT', '').replace('USDC', '').replace('BUSD', '')
    base_symbol = base_symbol.lower()

    return base_symbol in COINGECKO_SYMBOL_MAPPINGS

def get_supported_symbols() -> List[str]:
    return [symbol.upper() for symbol in COINGECKO_SYMBOL_MAPPINGS.keys()]

def get_symbol_info(symbol: str) -> Dict[str, str]:
    if not symbol or not isinstance(symbol, str):
        return {
            'original': symbol,
            'base': '',
            'coingecko_id': '',
            'supported': False
        }

    base_symbol = symbol.upper().replace('USDT', '').replace('USDC', '').replace('BUSD', '')
    base_symbol_lower = base_symbol.lower()

    coingecko_id = COINGECKO_SYMBOL_MAPPINGS.get(base_symbol_lower, base_symbol_lower)
    supported = base_symbol_lower in COINGECKO_SYMBOL_MAPPINGS

    return {
        'original': symbol,
        'base': base_symbol,
        'coingecko_id': coingecko_id,
        'supported': supported
    }

def validate_symbol_for_coingecko(symbol: str) -> Optional[str]:
    if not is_symbol_supported(symbol):
        logger.warning(f"Symbol '{symbol}' is not supported for CoinGecko API")
        return None

    return get_coingecko_id(symbol)

def add_symbol_mapping(symbol: str, coingecko_id: str) -> bool:
    try:
        if not symbol or not coingecko_id:
            return False

        base_symbol = symbol.lower().replace('usdt', '').replace('usdc', '').replace('busd', '')
        COINGECKO_SYMBOL_MAPPINGS[base_symbol] = coingecko_id

        logger.info(f"Added symbol mapping: {base_symbol} -> {coingecko_id}")
        return True

    except Exception as e:
        logger.error(f"Error adding symbol mapping {symbol} -> {coingecko_id}: {e}")
        return False

def get_mapping_stats() -> Dict[str, int]:
    return {
        'total_mappings': len(COINGECKO_SYMBOL_MAPPINGS),
        'major_coins': len([k for k in COINGECKO_SYMBOL_MAPPINGS.keys()
                           if k in ['btc', 'eth', 'bnb', 'ada', 'sol', 'dot', 'link']]),
        'meme_tokens': len([k for k in COINGECKO_SYMBOL_MAPPINGS.keys()
                           if k in ['pepe', 'doge', 'shib', 'bonk', 'wif']])
    }

def normalize_symbol_for_coingecko(symbol: str) -> str:
    return get_coingecko_id(symbol)

def is_valid_coingecko_symbol(symbol: str) -> bool:
    return is_symbol_supported(symbol)
