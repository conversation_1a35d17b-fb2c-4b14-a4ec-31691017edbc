import yaml
import logging

logger = logging.getLogger(__name__)
_config = None

def load_config():
    global _config
    if _config is not None:
        return _config
    try:
        with open('config.yaml', 'r', encoding='utf-8') as file:
            _config = yaml.safe_load(file)
            return _config
    except FileNotFoundError:
        logger.error("Không tìm thấy file config.yaml.")
        raise
    except yaml.YAMLError as e:
        logger.error(f"Lỗi khi đọc file config.yaml: {str(e)}")
        raise

def save_config(config):
    global _config
    try:
        with open('config.yaml', 'w', encoding='utf-8') as file:
            yaml.dump(config, file, default_flow_style=False)
        _config = config
        return True
    except Exception as e:
        logger.error(f"Lỗi khi lưu cấu hình: {str(e)}")
        return False

def get_binance_credentials():
    config = load_config()
    binance_config = config.get('binance', {})
    return {
        "api_key": binance_config.get('api_key', ''),
        "api_secret": binance_config.get('api_secret', ''),
        "testnet": binance_config.get('testnet', True)
    }

def get_binance_api_key():
    config = load_config()
    binance_config = config.get('binance', {})
    return binance_config.get('api_key', '')

def get_binance_api_secret():
    config = load_config()
    binance_config = config.get('binance', {})
    return binance_config.get('api_secret', '')

def get_discord_token():
    config = load_config()
    discord_config = config.get('discord', {})
    return discord_config.get('token', '')

def get_admin_id():
    config = load_config()
    discord_config = config.get('discord', {})
    return discord_config.get('admin_id', '')

def get_guild_id():
    config = load_config()
    discord_config = config.get('discord', {})
    return discord_config.get('guild_id', '')
def get_watchlist_symbols():
    from services.core.symbol_service import is_valid_symbol, normalize_symbol

    config = load_config()
    market_config = config.get('market', {})
    symbols = market_config.get('watchlist_symbols', ['BTCUSDT', 'ETHUSDT'])

    if not isinstance(symbols, list):
        logger.warning("WATCHLIST_SYMBOLS trong config.yaml không phải dạng list. Sử dụng mặc định ['BTCUSDT', 'ETHUSDT'].")
        symbols = ['BTCUSDT', 'ETHUSDT']

    seen = set()
    valid_symbols = []

    for symbol in symbols:
        if not isinstance(symbol, str):
            logger.warning(f"Symbol không phải chuỗi: {symbol}")
            continue

        symbol = normalize_symbol(symbol.strip().replace('/', ''))

        if symbol in seen:
            continue

        seen.add(symbol)

        if is_valid_symbol(symbol):
            valid_symbols.append(symbol)
        else:
            logger.warning(f"Symbol không hợp lệ trong WATCHLIST_SYMBOLS: {symbol}")

    if not valid_symbols:
        logger.warning("Không có symbol hợp lệ nào trong WATCHLIST_SYMBOLS. Sử dụng mặc định ['BTCUSDT', 'ETHUSDT'].")
        valid_symbols = ['BTCUSDT', 'ETHUSDT']

    return valid_symbols

def get_cryptocompare_api_key():
    config = load_config()
    return config.get('CRYPTOCOMPARE_API_KEY', '')